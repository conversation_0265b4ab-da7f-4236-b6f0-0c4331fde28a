#!/usr/bin/env python3
"""
测试共享RGB球面高斯模型的功能
"""

import torch
import numpy as np
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB
from utils.graphics_utils import BasicPointCloud

def test_shared_rgb_model():
    """测试共享RGB模型的基本功能"""
    print("=== 测试共享RGB球面高斯模型 ===")
    
    # 创建模型
    max_sg_degree = 3
    model = SphericalGaussianModelSharedRGB(max_sg_degree)
    
    # 创建测试点云
    num_points = 100
    points = np.random.randn(num_points, 3).astype(np.float32)
    colors = np.random.rand(num_points, 3).astype(np.float32)
    normals = np.random.randn(num_points, 3).astype(np.float32)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
    
    # 初始化模型
    model.create_from_pcd(pcd, spatial_lr_scale=1.0)
    
    print(f"初始化完成:")
    print(f"  - 点数: {model.num_primitives}")
    print(f"  - 最大SG度数: {model.max_sg_degree}")
    print(f"  - 当前激活SG度数: {model.active_sg_degree}")
    
    # 测试共享RGB的形状
    print(f"\n共享RGB张量形状:")
    print(f"  - _sg_rgb: {model._sg_rgb.shape}")  # 应该是 [N, 3] - 每个高斯一个RGB
    print(f"  - get_sg_rgb(): {model.get_sg_rgb.shape}")  # 应该是 [N, max_sg_degree, 3]
    
    # 测试sharpness权重计算
    print(f"\nSharpness权重测试:")
    sharpness = model.get_sg_sharpness
    print(f"  - sharpness形状: {sharpness.shape}")  # [N, max_sg_degree, 1]
    
    # 测试颜色计算
    print(f"\n颜色计算测试:")
    rgb_base = model.get_rgb_base
    sg_rgb = model.get_sg_rgb
    print(f"  - rgb_base形状: {rgb_base.shape}")  # [N, 3]
    print(f"  - sg_rgb形状: {sg_rgb.shape}")     # [N, max_sg_degree, 3]
    
    # 验证共享RGB的一致性
    print(f"\n验证每个高斯内部RGB共享:")
    shared_rgb_per_gaussian = model._sg_rgb  # [N, 3] - 每个高斯的共享RGB
    computed_rgb = model.get_sg_rgb  # [N, max_sg_degree, 3]

    # 检查每个高斯内部所有轴是否基于相同的RGB（通过sharpness权重调制）
    print(f"  - 每个高斯的共享RGB形状: {shared_rgb_per_gaussian.shape}")
    print(f"  - 计算后的RGB形状: {computed_rgb.shape}")

    # 验证第一个高斯的所有轴RGB是否都基于同一个共享RGB
    if num_points > 0:
        first_gaussian_shared = shared_rgb_per_gaussian[0]  # [3]
        first_gaussian_computed = computed_rgb[0]  # [max_sg_degree, 3]
        print(f"  - 第一个高斯的共享RGB: {first_gaussian_shared}")
        print(f"  - 第一个高斯各轴的RGB范围: min={first_gaussian_computed.min().item():.4f}, max={first_gaussian_computed.max().item():.4f}")
    
    # 测试轴数记录
    print(f"\n轴数记录测试:")
    axis_counts = model.get_sg_axis_count
    print(f"  - 轴数形状: {axis_counts.shape}")  # [N]
    print(f"  - 轴数范围: min={axis_counts.min().item()}, max={axis_counts.max().item()}")
    
    # 测试激活度数增加
    print(f"\n测试激活度数增加:")
    original_degree = model.active_sg_degree
    model.oneupSGdegree()
    new_degree = model.active_sg_degree
    print(f"  - 原始度数: {original_degree}")
    print(f"  - 新度数: {new_degree}")
    
    # 测试剪枝功能
    print(f"\n测试剪枝功能:")
    original_axis_sum = model.get_sg_axis_count.sum().item()
    print(f"  - 剪枝前总轴数: {original_axis_sum}")
    
    # 执行剪枝（使用较高的阈值来确保有轴被剪枝）
    model.cull_low_sharpness_axes(sharpness_threshold=0.5)
    
    new_axis_sum = model.get_sg_axis_count.sum().item()
    print(f"  - 剪枝后总轴数: {new_axis_sum}")
    print(f"  - 剪枝掉的轴数: {original_axis_sum - new_axis_sum}")
    
    print("\n=== 测试完成 ===")

def test_comparison_with_original():
    """比较共享RGB模型与原始模型的差异"""
    print("\n=== 比较共享RGB模型与原始模型 ===")
    
    # 这里可以添加与原始模型的比较测试
    # 由于原始模型文件可能不在当前路径，这里只做概念性说明
    
    print("主要差异:")
    print("1. 原始模型: _sg_rgb形状为[N, max_sg_degree, 3] - 每个高斯的每个轴都有独立RGB")
    print("2. 共享RGB模型: _sg_rgb形状为[N, 3] - 每个高斯的所有轴共用一个RGB")
    print("3. 共享RGB模型使用sharpness作为权重来调制每个高斯内部的共享RGB")
    print("4. 这样可以减少参数数量，每个高斯从max_sg_degree*3个RGB参数减少到3个")

    # 计算参数数量差异
    max_sg_degree = 3
    num_points = 10000

    original_rgb_params = num_points * max_sg_degree * 3
    shared_rgb_params = num_points * 3  # 每个高斯一个RGB

    print(f"\n参数数量比较 (假设{num_points}个点, {max_sg_degree}个SG轴):")
    print(f"  - 原始模型RGB参数: {original_rgb_params:,}")
    print(f"  - 共享RGB模型RGB参数: {shared_rgb_params:,}")
    print(f"  - 参数减少: {original_rgb_params - shared_rgb_params:,} ({(1-shared_rgb_params/original_rgb_params)*100:.1f}%)")

if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print("CUDA可用，使用GPU进行测试")
        device = "cuda"
    else:
        print("CUDA不可用，使用CPU进行测试")
        device = "cpu"
    
    try:
        test_shared_rgb_model()
        test_comparison_with_original()
        print("\n✅ 所有测试通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
