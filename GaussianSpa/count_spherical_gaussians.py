#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
读取spherical_gaussian_model_cullSG.py中输出的checkpoint或.ply文件，获取对应的高斯数量和球面高斯基轴数统计
"""

import os
import sys
import torch
import argparse
import numpy as np
from collections import Counter
from plyfile import PlyData


def analyze_axis_statistics(axis_counts, verbose=False):
    """
    分析球面高斯基轴数统计
    """
    print("\n=== 球面高斯基轴数统计 ===")

    # 计算总轴数
    total_axes = np.sum(axis_counts)
    total_gaussians = len(axis_counts)
    print(f"球面高斯基总轴数: {total_axes}")
    print(f"平均每个高斯的轴数: {total_axes / total_gaussians:.2f}")

    # 统计不同轴数的高斯数量
    unique_counts, frequencies = np.unique(axis_counts, return_counts=True)

    print(f"\n轴数分布:")
    for axis_count, frequency in zip(unique_counts, frequencies):
        percentage = (frequency / len(axis_counts)) * 100
        # 创建简单的条形图
        bar_length = int(percentage / 2)  # 每2%一个字符
        bar = "█" * bar_length
        print(f"  {axis_count} 轴: {frequency:6d} 个高斯 ({percentage:5.2f}%) {bar}")

    # 计算统计信息
    mean_axes = np.mean(axis_counts)
    median_axes = np.median(axis_counts)
    std_axes = np.std(axis_counts)
    min_axes = np.min(axis_counts)
    max_axes = np.max(axis_counts)

    print(f"\n轴数统计信息:")
    print(f"  平均轴数: {mean_axes:.3f}")
    print(f"  中位数轴数: {median_axes:.3f}")
    print(f"  标准差: {std_axes:.3f}")
    print(f"  最小轴数: {min_axes}")
    print(f"  最大轴数: {max_axes}")

    # 计算剪枝效果（假设最大轴数是原始轴数）
    if max_axes > 0:
        original_total_axes = total_gaussians * max_axes
        pruning_ratio = (original_total_axes - total_axes) / original_total_axes * 100
        print(f"\n剪枝效果分析:")
        print(f"  原始总轴数（假设）: {original_total_axes}")
        print(f"  当前总轴数: {total_axes}")
        print(f"  剪枝比例: {pruning_ratio:.2f}%")

    if verbose:
        # 显示更详细的分布信息
        print(f"\n详细分布信息:")
        percentiles = [25, 50, 75, 90, 95, 99]
        for p in percentiles:
            value = np.percentile(axis_counts, p)
            print(f"  {p}% 分位数: {value:.2f}")

        # 显示零轴高斯的数量
        zero_axis_count = np.sum(axis_counts == 0)
        if zero_axis_count > 0:
            print(f"\n零轴高斯数量: {zero_axis_count} ({zero_axis_count/total_gaussians*100:.2f}%)")


def count_gaussians_from_checkpoint(checkpoint_path, verbose=False):
    """
    从checkpoint文件中读取高斯数量和球面高斯基轴数统计
    """
    print(f"正在读取checkpoint: {checkpoint_path}")

    try:
        # 加载checkpoint
        checkpoint_data = torch.load(checkpoint_path, map_location=torch.device('cpu'))

        # 检查checkpoint格式
        if isinstance(checkpoint_data, tuple) and len(checkpoint_data) == 2:
            # 标准格式: (model_params, iteration)
            model_params, iteration = checkpoint_data
            print(f"Checkpoint迭代次数: {iteration}")
        else:
            # 只有模型参数
            model_params = checkpoint_data
            print("无法确定Checkpoint迭代次数")

        # 检查model_params的格式
        if isinstance(model_params, dict):
            # 从SphericalGaussianModelcullSG.capture()返回的字典格式
            if "xyz" in model_params:
                xyz = model_params["xyz"]
                num_gaussians = xyz.shape[0]
                print(f"高斯数量: {num_gaussians}")

                # 打印球面高斯基信息
                if "active_sg_degree" in model_params:
                    print(f"活跃球面高斯基度数: {model_params['active_sg_degree']}")

                # 打印其他相关信息
                if verbose:
                    if "sg_directions" in model_params:
                        sg_directions = model_params["sg_directions"]
                        print(f"球面高斯基方向张量形状: {sg_directions.shape}")
                    if "sg_sharpness" in model_params:
                        sg_sharpness = model_params["sg_sharpness"]
                        print(f"球面高斯基锐度张量形状: {sg_sharpness.shape}")
                    if "sg_rgb" in model_params:
                        sg_rgb = model_params["sg_rgb"]
                        print(f"球面高斯基RGB张量形状: {sg_rgb.shape}")

                # 分析轴数统计
                if "sg_axis_count" in model_params:
                    axis_counts = model_params["sg_axis_count"]
                    if hasattr(axis_counts, 'cpu'):
                        axis_counts = axis_counts.cpu().numpy()

                    analyze_axis_statistics(axis_counts, verbose)
                else:
                    print("警告: 未找到球面高斯基轴数信息")

                return num_gaussians
            else:
                print("错误: 未找到xyz数据")

        elif isinstance(model_params, tuple):
            # 兼容旧格式的元组
            if len(model_params) >= 2:
                xyz = model_params[1]
                if hasattr(xyz, 'shape'):
                    num_gaussians = xyz.shape[0]
                    print(f"高斯数量: {num_gaussians}")
                    print("注意: 这是旧格式的checkpoint，无法获取球面高斯基轴数信息")
                    return num_gaussians
                else:
                    print("错误: xyz没有shape属性")
            else:
                print(f"错误: model_params元组长度不足 ({len(model_params)})")
        else:
            print(f"错误: model_params格式不支持，类型为 {type(model_params)}")

    except Exception as e:
        print(f"读取checkpoint时出错: {e}")
        import traceback
        traceback.print_exc()

    return None


def count_gaussians_from_ply(ply_path, verbose=False):
    """
    从PLY文件中读取高斯数量和球面高斯基轴数统计
    """
    print(f"正在读取PLY文件: {ply_path}")

    try:
        # 读取PLY文件
        plydata = PlyData.read(ply_path)

        # 计算高斯数量
        total_gaussians = 0

        # 检查是否有多个顶点组（对应不同的SH度）
        vertex_groups = []
        for i, element in enumerate(plydata.elements):
            if element.name.startswith('vertex') or element.name == 'vertex':
                vertex_groups.append(element)
                print(f"顶点组 {element.name}: {len(element.data)} 个高斯")
                total_gaussians += len(element.data)

        if not vertex_groups:
            print("警告: 未找到顶点组")
            return None

        print(f"总高斯数量: {total_gaussians}")

        # 分析球面高斯基轴数（如果存在）
        main_element = vertex_groups[0]  # 使用第一个顶点组

        if verbose:
            print(f"\nPLY文件属性列表:")
            for name in main_element.data.dtype.names:
                if name.startswith('sg_'):
                    print(f"  {name}")

        # 检查是否有轴数信息
        if 'sg_axis_count' in main_element.data.dtype.names:
            axis_counts = np.asarray(main_element.data['sg_axis_count'])
            analyze_axis_statistics(axis_counts, verbose)

            # 额外分析：检查球面高斯基数据的一致性
            if verbose:
                print(f"\n=== 数据一致性检查 ===")
                # 检查是否有球面高斯基相关的其他属性
                sg_attributes = [name for name in main_element.data.dtype.names if name.startswith('sg_')]
                print(f"找到 {len(sg_attributes)} 个球面高斯基相关属性")

                # 尝试推断最大轴数
                max_axis_from_attributes = 0
                for attr in sg_attributes:
                    if '_' in attr and attr.split('_')[1].isdigit():
                        axis_idx = int(attr.split('_')[1])
                        max_axis_from_attributes = max(max_axis_from_attributes, axis_idx + 1)

                if max_axis_from_attributes > 0:
                    print(f"从属性名推断的最大轴数: {max_axis_from_attributes}")
                    actual_max_axis = np.max(axis_counts)
                    print(f"实际记录的最大轴数: {actual_max_axis}")

                    if max_axis_from_attributes != actual_max_axis:
                        print("警告: 属性数量与记录的轴数不一致！")
        else:
            print("注意: PLY文件中未找到球面高斯基轴数信息")

        # 如果有多个顶点组，打印每个组的信息
        if len(vertex_groups) > 1:
            for i, group in enumerate(vertex_groups):
                print(f"  顶点组 {i} ({group.name}): {len(group.data)} 个高斯")

        return total_gaussians

    except Exception as e:
        print(f"读取PLY文件时出错: {e}")
        import traceback
        traceback.print_exc()

    return None


def main():
    parser = argparse.ArgumentParser(description="读取spherical_gaussian_model_cullSG.py中输出的checkpoint或.ply文件，获取对应的高斯数量和球面高斯基轴数统计")
    parser.add_argument("file_path", help="checkpoint或.ply文件的路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    args = parser.parse_args()

    if not os.path.exists(args.file_path):
        print(f"错误: 文件不存在 - {args.file_path}")
        return 1

    print("=" * 60)
    print("球面高斯模型统计分析工具")
    print("=" * 60)

    # 根据文件扩展名决定使用哪个函数
    if args.file_path.lower().endswith('.pth'):
        result = count_gaussians_from_checkpoint(args.file_path, args.verbose)
    elif args.file_path.lower().endswith('.ply'):
        result = count_gaussians_from_ply(args.file_path, args.verbose)
    else:
        print(f"错误: 不支持的文件类型 - {args.file_path}")
        print("支持的文件类型: .pth (checkpoint), .ply")
        return 1

    if result is not None:
        print("\n" + "=" * 60)
        print(f"分析完成！总高斯数量: {result}")
        print("=" * 60)
    else:
        print("\n分析失败！")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
