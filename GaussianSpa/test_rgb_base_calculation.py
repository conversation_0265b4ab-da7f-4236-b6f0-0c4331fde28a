#!/usr/bin/env python3
"""
测试基于rgb_base比例的RGB计算方式
"""

import torch
import numpy as np
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB
from utils.graphics_utils import BasicPointCloud

def test_rgb_base_calculation():
    """测试基于rgb_base的RGB计算方式"""
    print("=== 测试基于rgb_base的RGB计算方式 ===")
    
    # 创建模型
    max_sg_degree = 3
    model = SphericalGaussianModelSharedRGB(max_sg_degree)
    
    # 创建测试点云
    num_points = 2  # 使用两个点便于观察
    points = np.array([[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]], dtype=np.float32)
    colors = np.array([[0.5, 0.5, 0.5], [0.8, 0.3, 0.2]], dtype=np.float32)
    normals = np.array([[0.0, 0.0, 1.0], [1.0, 0.0, 0.0]], dtype=np.float32)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
    
    # 初始化模型
    model.create_from_pcd(pcd, spatial_lr_scale=1.0)
    model.active_sg_degree = max_sg_degree
    
    # 手动设置已知的值便于验证
    with torch.no_grad():
        # 设置rgb_base
        model._rgb_base[0, :] = torch.tensor([0.2, 0.4, 0.6], device="cuda")  # 第一个高斯
        model._rgb_base[1, :] = torch.tensor([0.1, 0.3, 0.5], device="cuda")  # 第二个高斯
        
        # 设置共享RGB
        model._sg_rgb[0, :] = torch.tensor([1.0, 2.0, 3.0], device="cuda")  # 第一个高斯的共享RGB
        model._sg_rgb[1, :] = torch.tensor([2.0, 1.0, 0.5], device="cuda")  # 第二个高斯的共享RGB
    
    print(f"初始设置:")
    print(f"  高斯0 - rgb_base: {model._rgb_base[0, :].tolist()}")
    print(f"  高斯0 - sg_rgb: {model._sg_rgb[0, :].tolist()}")
    print(f"  高斯1 - rgb_base: {model._rgb_base[1, :].tolist()}")
    print(f"  高斯1 - sg_rgb: {model._sg_rgb[1, :].tolist()}")
    
    # 计算get_sg_rgb的结果
    computed_sg_rgb = model.get_sg_rgb
    print(f"\n计算结果 (get_sg_rgb):")
    print(f"  形状: {computed_sg_rgb.shape}")  # 应该是 [2, 3, 3]
    
    # 验证计算逻辑
    print(f"\n验证计算逻辑:")
    for gaussian_idx in range(2):
        rgb_base = model._rgb_base[gaussian_idx, :]  # [3]
        sg_rgb = model._sg_rgb[gaussian_idx, :]      # [3]
        
        # 手动计算期望结果
        expected_result = sg_rgb * rgb_base  # [3]
        
        print(f"  高斯{gaussian_idx}:")
        print(f"    rgb_base: {rgb_base.tolist()}")
        print(f"    sg_rgb: {sg_rgb.tolist()}")
        print(f"    期望结果 (sg_rgb * rgb_base): {expected_result.tolist()}")
        
        # 检查每个轴的计算结果
        for axis_idx in range(max_sg_degree):
            computed_axis_rgb = computed_sg_rgb[gaussian_idx, axis_idx, :]
            print(f"    轴{axis_idx}计算结果: {computed_axis_rgb.tolist()}")
            
            # 验证是否一致
            is_consistent = torch.allclose(computed_axis_rgb, expected_result, atol=1e-6)
            print(f"    轴{axis_idx}是否一致: {is_consistent}")
    
    # 对比新旧计算方式
    print(f"\n对比新旧计算方式:")
    print(f"  旧方式 (基于sharpness权重):")
    print(f"    - 每个轴的权重不同，基于sharpness归一化")
    print(f"    - 轴间RGB会有差异")
    print(f"  新方式 (基于rgb_base比例):")
    print(f"    - 每个轴的RGB相同，都是 sg_rgb * rgb_base")
    print(f"    - 轴间RGB完全一致")
    
    # 测试剪枝功能
    print(f"\n测试剪枝功能:")
    
    # 设置一些低sharpness值
    with torch.no_grad():
        model._sg_sharpness[0, 1, 0] = 0.005  # 第一个高斯的第二个轴设为低sharpness
        model._sg_sharpness[1, 0, 0] = 0.008  # 第二个高斯的第一个轴设为低sharpness
        model._sg_sharpness[1, 2, 0] = 0.006  # 第二个高斯的第三个轴设为低sharpness
    
    print(f"剪枝前:")
    print(f"  高斯0轴数: {model.get_sg_axis_count[0].item()}")
    print(f"  高斯1轴数: {model.get_sg_axis_count[1].item()}")
    print(f"  高斯0 rgb_base: {model._rgb_base[0, :].tolist()}")
    print(f"  高斯1 rgb_base: {model._rgb_base[1, :].tolist()}")
    
    # 执行剪枝
    model.cull_low_sharpness_axes(sharpness_threshold=0.01)
    
    print(f"剪枝后:")
    print(f"  高斯0轴数: {model.get_sg_axis_count[0].item()}")
    print(f"  高斯1轴数: {model.get_sg_axis_count[1].item()}")
    print(f"  高斯0 rgb_base: {model._rgb_base[0, :].tolist()}")
    print(f"  高斯1 rgb_base: {model._rgb_base[1, :].tolist()}")
    print(f"  高斯0 sg_rgb: {model._sg_rgb[0, :].tolist()}")
    print(f"  高斯1 sg_rgb: {model._sg_rgb[1, :].tolist()}")

def test_comparison_with_gaussian_model_simple():
    """与gaussian_model_simple.py的计算方式进行对比"""
    print("\n=== 与gaussian_model_simple.py计算方式对比 ===")
    
    print("gaussian_model_simple.py中的计算方式:")
    print("  shared_rest = self._features_rest @ self._features_dc")
    print("  - _features_rest: [N, 3, SH_coeffs-1]")
    print("  - _features_dc: [N, 3, 1]")
    print("  - 结果: [N, 3, SH_coeffs-1]")
    print("  - 使用矩阵乘法，dc分量作为比例因子")
    
    print("\n我们的计算方式:")
    print("  weighted_rgb = shared_rgb_expanded * rgb_base_expanded")
    print("  - _sg_rgb: [N, 3] -> expanded to [N, max_sg_degree, 3]")
    print("  - _rgb_base: [N, 3] -> expanded to [N, max_sg_degree, 3]")
    print("  - 结果: [N, max_sg_degree, 3]")
    print("  - 使用逐元素乘法，rgb_base作为比例因子")
    
    print("\n相似性:")
    print("  1. 都使用基础特征作为比例因子")
    print("  2. 都是将共享参数与基础参数结合")
    print("  3. 都实现了参数的动态调制")
    
    print("\n差异:")
    print("  1. gaussian_model_simple使用矩阵乘法(@)")
    print("  2. 我们使用逐元素乘法(*)")
    print("  3. 我们的方式更直观，每个轴的RGB完全相同")

if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print("CUDA可用，使用GPU进行测试")
        device = "cuda"
    else:
        print("CUDA不可用，使用CPU进行测试")
        device = "cpu"
    
    try:
        test_rgb_base_calculation()
        test_comparison_with_gaussian_model_simple()
        print("\n✅ 所有测试通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
