# -*- coding:utf-8 -*-
# 
# Author: 
# Time: 

import torch
import fnmatch
import numpy as np
import os


class OptimizingSpaHQS:
    def __init__(self, gaussians, opt, device, imp_score_flag=False):
        self.gaussians = gaussians
        self.device = device
        self.imp_score_flag = imp_score_flag
        self.mu = opt.rho_lr  # 直接复用ADMM的rho
        self.prune_ratio = opt.prune_ratio2
        self.z = torch.Tensor(gaussians.get_opacity.data.clone()).to(device)

    def update(self, imp_score=None, *args,**kwargs):
        theta = self.gaussians.get_opacity
        if self.imp_score_flag:
            self.z = self.prune_z_metrics_imp_score(theta, imp_score)
        else:
            self.z = self.prune_z(theta)

    def prune_z(self, theta):
        index = int(self.prune_ratio * len(theta))
        theta_sorted, _ = torch.sort(theta.abs(), descending=True)
        threshold = theta_sorted[index-1]
        return (theta.abs() >= threshold) * theta

    def prune_z_metrics_imp_score(self, theta, imp_score):
        index = int(self.prune_ratio * len(imp_score))
        imp_score_sorted, _ = torch.sort(imp_score, descending=True)
        threshold = imp_score_sorted[index-1]
        return (imp_score >= threshold) * theta

    def append_spa_loss(self, loss):
        loss += 0.5 * self.mu * torch.norm(self.gaussians.get_opacity - self.z, p=2)**2
        return loss

    def adjust_mu(self, iteration, iterations, factor=5):
        if iteration > int(0.85 * iterations):
            self.mu = factor * self.mu


