# 共享 RGB 球面高斯模型 (Shared RGB Spherical Gaussian Model)

## 概述

这是对原始球面高斯模型的改进版本，主要特点是每个高斯的所有球面高斯轴共用一个 RGB 颜色参数，并使用 sharpness 作为权重来调制颜色，参考了`gaussian_model_simple.py`中`features_rest`的计算方法。

## 主要改进

### 1. 共享 RGB 设计

- **原始模型**: `_sg_rgb` 形状为 `[N, max_sg_degree, 3]`，每个高斯的每个轴都有独立的 RGB
- **共享 RGB 模型**: `_sg_rgb` 形状为 `[N, 3]`，每个高斯的所有轴共用一个 RGB

### 2. Sharpness 权重调制

```python
# 获取归一化的sharpness作为权重 [N, max_sg_degree, 1]
sharpness_weights = self.get_sg_sharpness

# 归一化sharpness权重（按轴维度，每个高斯内部归一化）
sharpness_sum = torch.sum(sharpness_weights, dim=1, keepdim=True) + 1e-8
normalized_weights = sharpness_weights / sharpness_sum

# 每个高斯的共享RGB [N, 3] -> [N, 1, 3] -> [N, max_sg_degree, 3]
shared_rgb_per_gaussian = self._sg_rgb.unsqueeze(1)
shared_rgb_expanded = shared_rgb_per_gaussian.expand(-1, self.max_sg_degree, -1)

# 使用sharpness权重调制每个高斯的共享RGB [N, max_sg_degree, 3]
weighted_rgb = shared_rgb_expanded * normalized_weights
```

### 3. 参数数量减少

对于 N 个高斯点和 max_sg_degree 个轴：

- **原始模型**: `N × max_sg_degree × 3` 个 RGB 参数
- **共享 RGB 模型**: `N × 3` 个 RGB 参数
- **参数减少**: 约 `(max_sg_degree-1)/max_sg_degree × 100%`

## 文件结构

```
GaussianSpa/
├── scene/
│   ├── spherical_gaussian_model_cullSG.py          # 原始模型
│   └── spherical_gaussian_model_shared_rgb.py      # 新的共享RGB模型
├── train_imp_score_spherical_gaussian_cullSG.py    # 原始训练脚本
├── train_spherical_gaussian_shared_rgb.py          # 新的训练脚本
├── test_shared_rgb_model.py                        # 测试脚本
└── README_shared_rgb.md                            # 本文档
```

## 使用方法

### 1. 训练

```bash
python train_spherical_gaussian_shared_rgb.py \
    --source_path /path/to/your/data \
    --model_path /path/to/output \
    --imp_metric outdoor \
    --iterations 50000
```

### 2. 测试模型功能

```bash
python test_shared_rgb_model.py
```

### 3. 在代码中使用

```python
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB

# 创建模型
gaussians = SphericalGaussianModelSharedRGB(max_sg_degree=3)

# 从点云初始化
gaussians.create_from_pcd(pcd, spatial_lr_scale=1.0)

# 获取共享RGB调制后的颜色
rgb_colors = gaussians.get_sg_rgb  # [N, max_sg_degree, 3]
```

## 核心改动说明

### 1. 模型初始化

```python
# 原始: 每个高斯的每个轴独立的RGB
self._sg_rgb = torch.empty(0)  # [N, max_sg_degree, 3]

# 共享RGB: 每个高斯的所有轴共享一个RGB
self._sg_rgb = torch.empty(0)  # [N, 3]
```

### 2. RGB 计算方法

```python
@property
def get_sg_rgb(self):
    """动态生成基于sharpness权重的RGB颜色"""
    # 获取sharpness权重并归一化
    sharpness_weights = self.get_sg_sharpness
    sharpness_sum = torch.sum(sharpness_weights, dim=1, keepdim=True) + 1e-8
    normalized_weights = sharpness_weights / sharpness_sum

    # 使用权重调制每个高斯的共享RGB
    shared_rgb_per_gaussian = self._sg_rgb.unsqueeze(1)
    shared_rgb_expanded = shared_rgb_per_gaussian.expand(-1, self.max_sg_degree, -1)
    weighted_rgb = shared_rgb_expanded * normalized_weights

    return weighted_rgb
```

### 3. 训练优化器设置

```python
# 添加共享RGB参数到优化器
if self.max_sg_degree > 0:
    l.append({'params': [self._sg_rgb], 'lr': training_args.feature_lr, "name": "sg_rgb"})
```

### 4. 密度化处理

由于每个高斯有自己的共享 RGB，在 densify_and_split 和 densify_and_clone 中仍需要复制 RGB 参数：

```python
# 原始: 复制每个轴的RGB
new_sg_rgb = self._sg_rgb[selected_pts_mask].repeat(N,1,1)  # [N*2, max_sg_degree, 3]

# 共享RGB: 复制每个高斯的共享RGB
new_sg_rgb = self._sg_rgb[selected_pts_mask].repeat(N,1)    # [N*2, 3]
```

## 优势

1. **内存效率**: 减少 RGB 参数数量（从每个轴独立 RGB 到每个高斯共享 RGB）
2. **训练效率**: 更少的参数意味着更快的训练速度
3. **一致性**: 每个高斯内部的轴间颜色一致性
4. **灵活性**: 通过 sharpness 权重仍能实现轴间的差异化表现

## 注意事项

1. 确保在使用前已正确安装所有依赖
2. 共享 RGB 模型与原始模型的 checkpoint 不兼容
3. PLY 文件格式有所变化，包含了共享 RGB 信息
4. 测试脚本需要 CUDA 环境（如果可用）

## 兼容性

- 与原始 spherical_gaussian_renderer 兼容
- 与现有的训练 pipeline 兼容
- 支持所有原始模型的功能（剪枝、密度化等）
