#!/usr/bin/env python3
"""
测试RGB守恒逻辑的正确性
"""

import torch
import numpy as np
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB
from utils.graphics_utils import BasicPointCloud

def test_rgb_conservation():
    """测试RGB守恒逻辑"""
    print("=== 测试RGB守恒逻辑 ===")
    
    # 创建模型
    max_sg_degree = 3
    model = SphericalGaussianModelSharedRGB(max_sg_degree)
    
    # 创建测试点云
    num_points = 1  # 只用一个点便于分析
    points = np.array([[0.0, 0.0, 0.0]], dtype=np.float32)
    colors = np.array([[0.5, 0.5, 0.5]], dtype=np.float32)
    normals = np.array([[0.0, 0.0, 1.0]], dtype=np.float32)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
    
    # 初始化模型
    model.create_from_pcd(pcd, spatial_lr_scale=1.0)
    model.active_sg_degree = max_sg_degree
    
    # 手动设置已知的值
    with torch.no_grad():
        # 设置sharpness: [0.6, 0.3, 0.1] (总和=1.0，便于计算)
        model._sg_sharpness[0, 0, 0] = 0.6
        model._sg_sharpness[0, 1, 0] = 0.3
        model._sg_sharpness[0, 2, 0] = 0.1
        
        # 设置共享RGB
        model._sg_rgb[0, :] = torch.tensor([1.0, 2.0, 3.0], device="cuda")
        
        # 设置base RGB
        model._rgb_base[0, :] = torch.tensor([0.1, 0.2, 0.3], device="cuda")
    
    print(f"初始状态:")
    print(f"  - sharpness: {model.get_sg_sharpness[0, :, 0].tolist()}")
    print(f"  - 共享RGB: {model._sg_rgb[0, :].tolist()}")
    print(f"  - base RGB: {model._rgb_base[0, :].tolist()}")
    
    # 计算每个轴的RGB贡献
    sharpness = model.get_sg_sharpness[0, :, 0]
    shared_rgb = model._sg_rgb[0, :]
    total_sharpness = sharpness.sum()
    
    axis_contributions = []
    for i in range(3):
        weight = sharpness[i] / total_sharpness
        contribution = shared_rgb * weight
        axis_contributions.append(contribution)
        print(f"  - 轴{i}权重: {weight.item():.3f}, RGB贡献: {contribution.tolist()}")
    
    # 计算总的RGB表现（base + 所有轴贡献）
    total_rgb_before = model._rgb_base[0, :].clone()
    for contrib in axis_contributions:
        total_rgb_before += contrib
    
    print(f"  - 剪枝前总RGB表现: {total_rgb_before.tolist()}")
    
    # 执行剪枝：剪枝最后一个轴（sharpness=0.1）
    print(f"\n执行剪枝 (阈值=0.15，应该剪枝最后一个轴)...")
    
    base_rgb_before = model._rgb_base[0, :].clone()
    model.cull_low_sharpness_axes(sharpness_threshold=0.15)
    base_rgb_after = model._rgb_base[0, :]
    
    print(f"剪枝后状态:")
    print(f"  - 剩余轴数: {model.get_sg_axis_count[0].item()}")
    print(f"  - 剩余sharpness: {model.get_sg_sharpness[0, :model.get_sg_axis_count[0].item(), 0].tolist()}")
    print(f"  - 共享RGB: {model._sg_rgb[0, :].tolist()}")
    print(f"  - base RGB变化: {(base_rgb_after - base_rgb_before).tolist()}")
    
    # 计算剪枝后的总RGB表现
    remaining_axis_count = model.get_sg_axis_count[0].item()
    if remaining_axis_count > 0:
        remaining_sharpness = model.get_sg_sharpness[0, :remaining_axis_count, 0]
        remaining_total_sharpness = remaining_sharpness.sum()
        
        total_rgb_after = model._rgb_base[0, :].clone()
        for i in range(remaining_axis_count):
            weight = remaining_sharpness[i] / remaining_total_sharpness
            contribution = model._sg_rgb[0, :] * weight
            total_rgb_after += contribution
            print(f"  - 剩余轴{i}权重: {weight.item():.3f}, RGB贡献: {contribution.tolist()}")
    else:
        total_rgb_after = model._rgb_base[0, :].clone()
    
    print(f"  - 剪枝后总RGB表现: {total_rgb_after.tolist()}")
    
    # 验证RGB守恒
    rgb_diff = torch.abs(total_rgb_after - total_rgb_before)
    is_conserved = torch.all(rgb_diff < 1e-6)
    
    print(f"\nRGB守恒验证:")
    print(f"  - RGB差异: {rgb_diff.tolist()}")
    print(f"  - 是否守恒: {is_conserved.item()}")
    
    # 测试所有轴都被剪枝的情况
    print(f"\n测试所有轴都被剪枝的情况...")
    
    # 重新设置一个高斯
    with torch.no_grad():
        model._sg_sharpness[0, :, 0] = 0.001  # 所有轴都很小
        model._sg_rgb[0, :] = torch.tensor([1.0, 2.0, 3.0], device="cuda")
        model._rgb_base[0, :] = torch.tensor([0.1, 0.2, 0.3], device="cuda")
        model._sg_axis_count[0] = 3  # 重置轴数
    
    base_before = model._rgb_base[0, :].clone()
    shared_before = model._sg_rgb[0, :].clone()
    
    model.cull_low_sharpness_axes(sharpness_threshold=0.01)
    
    base_after = model._rgb_base[0, :]
    shared_after = model._sg_rgb[0, :]
    
    print(f"  - 轴数: 3 -> {model.get_sg_axis_count[0].item()}")
    print(f"  - base RGB: {base_before.tolist()} -> {base_after.tolist()}")
    print(f"  - 共享RGB: {shared_before.tolist()} -> {shared_after.tolist()}")
    print(f"  - 共享RGB是否被清零: {torch.allclose(shared_after, torch.zeros_like(shared_after))}")
    print(f"  - base RGB增加量: {(base_after - base_before).tolist()}")
    print(f"  - 是否等于原共享RGB: {torch.allclose(base_after - base_before, shared_before)}")

if __name__ == "__main__":
    torch.manual_seed(42)
    np.random.seed(42)
    
    if torch.cuda.is_available():
        print("CUDA可用，使用GPU进行测试")
    else:
        print("CUDA不可用，使用CPU进行测试")
    
    try:
        test_rgb_conservation()
        print("\n✅ RGB守恒测试完成!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
