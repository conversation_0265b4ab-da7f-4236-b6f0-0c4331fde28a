#!/usr/bin/env python3
"""
测试修改后的cull_low_sharpness_axes函数
"""

import torch
import numpy as np
from scene.spherical_gaussian_model_shared_rgb import SphericalGaussianModelSharedRGB
from utils.graphics_utils import BasicPointCloud

def test_cull_function():
    """测试剪枝函数的正确性"""
    print("=== 测试修改后的剪枝函数 ===")
    
    # 创建模型
    max_sg_degree = 3
    model = SphericalGaussianModelSharedRGB(max_sg_degree)
    
    # 创建测试点云
    num_points = 5  # 使用较少的点便于观察
    points = np.random.randn(num_points, 3).astype(np.float32)
    colors = np.random.rand(num_points, 3).astype(np.float32)
    normals = np.random.randn(num_points, 3).astype(np.float32)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
    
    # 初始化模型
    model.create_from_pcd(pcd, spatial_lr_scale=1.0)
    model.active_sg_degree = max_sg_degree  # 激活所有轴
    
    print(f"初始化完成:")
    print(f"  - 点数: {model.num_primitives}")
    print(f"  - 最大SG度数: {model.max_sg_degree}")
    print(f"  - 当前激活SG度数: {model.active_sg_degree}")
    
    # 手动设置一些sharpness值来测试剪枝
    with torch.no_grad():
        # 设置第一个高斯的sharpness: [0.5, 0.005, 0.3] (中间的轴会被剪枝)
        model._sg_sharpness[0, 0, 0] = 0.5   # 保留
        model._sg_sharpness[0, 1, 0] = 0.005 # 剪枝 (< 0.01)
        model._sg_sharpness[0, 2, 0] = 0.3   # 保留
        
        # 设置第二个高斯的sharpness: [0.008, 0.2, 0.006] (第一和第三轴会被剪枝)
        model._sg_sharpness[1, 0, 0] = 0.008 # 剪枝 (< 0.01)
        model._sg_sharpness[1, 1, 0] = 0.2   # 保留
        model._sg_sharpness[1, 2, 0] = 0.006 # 剪枝 (< 0.01)
        
        # 设置一些特定的RGB值便于观察
        model._sg_rgb[0, :] = torch.tensor([1.0, 0.5, 0.2], device="cuda")  # 第一个高斯的共享RGB
        model._sg_rgb[1, :] = torch.tensor([0.8, 0.3, 0.9], device="cuda")  # 第二个高斯的共享RGB
    
    print(f"\n剪枝前状态:")
    print(f"  - 总轴数: {model.get_sg_axis_count.sum().item()}")
    print(f"  - 各高斯轴数: {model.get_sg_axis_count[:2].tolist()}")
    
    # 显示剪枝前的sharpness和RGB
    print(f"\n剪枝前详细信息:")
    for i in range(2):  # 只显示前两个高斯
        sharpness = model.get_sg_sharpness[i, :, 0]
        rgb = model._sg_rgb[i, :]
        rgb_base = model._rgb_base[i, :]
        print(f"  高斯{i}:")
        print(f"    - sharpness: {sharpness.tolist()}")
        print(f"    - 共享RGB: {rgb.tolist()}")
        print(f"    - base RGB: {rgb_base.tolist()}")
    
    # 执行剪枝
    print(f"\n执行剪枝 (阈值=0.01)...")
    model.cull_low_sharpness_axes(sharpness_threshold=0.01)
    
    print(f"\n剪枝后状态:")
    print(f"  - 总轴数: {model.get_sg_axis_count.sum().item()}")
    print(f"  - 各高斯轴数: {model.get_sg_axis_count[:2].tolist()}")
    
    # 显示剪枝后的sharpness和RGB
    print(f"\n剪枝后详细信息:")
    for i in range(2):  # 只显示前两个高斯
        axis_count = model.get_sg_axis_count[i].item()
        sharpness = model.get_sg_sharpness[i, :axis_count, 0] if axis_count > 0 else torch.tensor([])
        rgb = model._sg_rgb[i, :]
        rgb_base = model._rgb_base[i, :]
        print(f"  高斯{i} (剩余{axis_count}个轴):")
        if axis_count > 0:
            print(f"    - 剩余sharpness: {sharpness.tolist()}")
        else:
            print(f"    - 剩余sharpness: []")
        print(f"    - 共享RGB: {rgb.tolist()}")
        print(f"    - base RGB: {rgb_base.tolist()}")
    
    # 验证剪枝逻辑
    print(f"\n验证剪枝逻辑:")
    print(f"  - 第一个高斯应该剩余2个轴 (0.5和0.3的sharpness)")
    print(f"  - 第二个高斯应该剩余1个轴 (0.2的sharpness)")
    print(f"  - base RGB应该增加了被剪枝轴的贡献")
    
    # 测试边界情况：所有轴都被剪枝
    print(f"\n测试边界情况：剪枝所有轴...")
    with torch.no_grad():
        # 将第三个高斯的所有sharpness设为很小的值
        model._sg_sharpness[2, :, 0] = 0.001

    axis_count_before = model.get_sg_axis_count[2].item()
    rgb_base_before = model._rgb_base[2, :].clone()
    shared_rgb_before = model._sg_rgb[2, :].clone()

    model.cull_low_sharpness_axes(sharpness_threshold=0.01)

    axis_count_after = model.get_sg_axis_count[2].item()
    rgb_base_after = model._rgb_base[2, :]
    shared_rgb_after = model._sg_rgb[2, :]

    print(f"  - 第三个高斯轴数: {axis_count_before} -> {axis_count_after}")
    print(f"  - base RGB变化: {(rgb_base_after - rgb_base_before).tolist()}")
    print(f"  - 共享RGB剪枝前: {shared_rgb_before.tolist()}")
    print(f"  - 共享RGB剪枝后: {shared_rgb_after.tolist()}")
    print(f"  - 共享RGB是否被清零: {torch.allclose(shared_rgb_after, torch.zeros_like(shared_rgb_after))}")

    # 验证RGB总量守恒
    expected_rgb_change = shared_rgb_before
    actual_rgb_change = rgb_base_after - rgb_base_before
    print(f"  - RGB总量是否守恒: {torch.allclose(expected_rgb_change, actual_rgb_change, atol=1e-6)}")

    print("\n=== 剪枝函数测试完成 ===")

def test_weight_calculation():
    """测试权重计算的正确性"""
    print("\n=== 测试权重计算 ===")
    
    # 模拟权重计算
    sharpness = torch.tensor([0.5, 0.005, 0.3])  # 第二个会被剪枝
    total_sharpness = sharpness.sum()
    
    print(f"原始sharpness: {sharpness.tolist()}")
    print(f"总sharpness: {total_sharpness.item()}")
    
    # 计算被剪枝轴的权重
    culled_axis_weight = sharpness[1] / total_sharpness
    print(f"被剪枝轴的权重: {culled_axis_weight.item():.4f}")
    
    # 剪枝后剩余轴的权重
    remaining_sharpness = torch.tensor([0.5, 0.3])
    remaining_total = remaining_sharpness.sum()
    remaining_weights = remaining_sharpness / remaining_total
    
    print(f"剩余sharpness: {remaining_sharpness.tolist()}")
    print(f"剩余权重: {remaining_weights.tolist()}")
    print(f"权重和: {remaining_weights.sum().item():.4f}")

if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print("CUDA可用，使用GPU进行测试")
        device = "cuda"
    else:
        print("CUDA不可用，使用CPU进行测试")
        device = "cpu"
    
    try:
        test_cull_function()
        test_weight_calculation()
        print("\n✅ 所有测试通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
