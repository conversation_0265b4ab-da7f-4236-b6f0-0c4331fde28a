Baseline directory: /ghome/l1/yliu/chenyikeng/gaussian-splatting-origin/output/bicycle_1200_eval
Current directory: output/bicycle/imp_score_opa_origin

Comparison method: Both baseline and current renders compared against ground truth
Color coding in difference images:
  - Red regions: Baseline render closer to ground truth
  - Blue regions: Current render closer to ground truth
  - Green regions: Similar distance to ground truth


==================================================
SSIM Metric - Comparison results for all 25 images:
==================================================

Statistics:
  Average difference: 0.015071
  Difference std dev: 0.006634
  Maximum difference: 0.024950
  Minimum difference: -0.002205
  Baseline average: 0.765461
  Current average: 0.780532

Performance change statistics:
  Improved images: 23 (92.0%)
  Degraded images: 2 (8.0%)
  Unchanged images: 0 (0.0%)

Detailed results (sorted by difference):
Rank   Image Name      Baseline     Current      Difference  
-----------------------------------------------------------------
1      00003.png       0.689966     0.714915     0.024950    
2      00014.png       0.812927     0.836830     0.023903    
3      00018.png       0.854497     0.877000     0.022502    
4      00001.png       0.545476     0.567090     0.021614    
5      00007.png       0.785581     0.806527     0.020946    
6      00000.png       0.616293     0.636892     0.020599    
7      00019.png       0.848776     0.868269     0.019493    
8      00020.png       0.811265     0.829344     0.018079    
9      00013.png       0.836192     0.854037     0.017845    
10     00015.png       0.808326     0.824737     0.016412    
11     00006.png       0.788042     0.804374     0.016332    
12     00005.png       0.732821     0.748403     0.015583    
13     00022.png       0.852306     0.867861     0.015555    
14     00009.png       0.790674     0.805863     0.015189    
15     00012.png       0.808325     0.823172     0.014847    
16     00008.png       0.725043     0.739605     0.014562    
17     00010.png       0.765049     0.779531     0.014482    
18     00024.png       0.833891     0.848335     0.014445    
19     00021.png       0.807550     0.821954     0.014404    
20     00016.png       0.789414     0.803287     0.013872    
21     00017.png       0.827470     0.839432     0.011962    
22     00023.png       0.736736     0.743997     0.007261    
23     00004.png       0.718262     0.722697     0.004435    
24     00002.png       0.586630     0.586332     -0.000298   
25     00011.png       0.765025     0.762821     -0.002205   

==================================================
PSNR Metric - Comparison results for all 25 images:
==================================================

Statistics:
  Average difference: 0.143382
  Difference std dev: 0.238861
  Maximum difference: 0.508659
  Minimum difference: -0.556028
  Baseline average: 25.241396
  Current average: 25.384778

Performance change statistics:
  Improved images: 19 (76.0%)
  Degraded images: 6 (24.0%)
  Unchanged images: 0 (0.0%)

Detailed results (sorted by difference):
Rank   Image Name      Baseline     Current      Difference  
-----------------------------------------------------------------
1      00014.png       27.290678    27.799337    0.508659    
2      00018.png       28.792381    29.217892    0.425510    
3      00006.png       25.695704    26.054695    0.358992    
4      00015.png       27.030905    27.384953    0.354048    
5      00020.png       26.992008    27.345413    0.353405    
6      00001.png       19.180836    19.513710    0.332874    
7      00022.png       28.599625    28.930489    0.330864    
8      00005.png       23.191059    23.502066    0.311007    
9      00021.png       27.118723    27.421762    0.303040    
10     00007.png       25.808685    26.099520    0.290834    
11     00013.png       27.473984    27.729971    0.255987    
12     00003.png       21.060360    21.310863    0.250504    
13     00024.png       26.942923    27.133215    0.190292    
14     00016.png       25.702860    25.858139    0.155279    
15     00017.png       26.911993    27.050739    0.138746    
16     00019.png       28.082325    28.176535    0.094210    
17     00010.png       24.783907    24.845169    0.061262    
18     00009.png       25.810268    25.846310    0.036041    
19     00004.png       22.141403    22.151367    0.009964    
20     00012.png       26.630146    26.606880    -0.023266   
21     00023.png       24.863947    24.780491    -0.083456   
22     00011.png       23.932665    23.786495    -0.146170   
23     00008.png       24.959988    24.789000    -0.170988   
24     00002.png       20.002327    19.805265    -0.197062   
25     00000.png       22.035208    21.479179    -0.556028   

==================================================
LPIPS Metric - Comparison results for all 25 images:
==================================================

Statistics:
  Average difference: -0.000705
  Difference std dev: 0.014121
  Maximum difference: 0.023106
  Minimum difference: -0.035979
  Baseline average: 0.209462
  Current average: 0.210167

Performance change statistics:
  Improved images: 14 (56.0%)
  Degraded images: 11 (44.0%)
  Unchanged images: 0 (0.0%)

Detailed results (sorted by difference):
Rank   Image Name      Baseline     Current      Difference  
-----------------------------------------------------------------
1      00014.png       0.216481     0.193374     0.023106    
2      00019.png       0.209824     0.188477     0.021347    
3      00013.png       0.159376     0.146074     0.013302    
4      00015.png       0.203427     0.192874     0.010553    
5      00018.png       0.173091     0.163424     0.009668    
6      00012.png       0.181551     0.172670     0.008881    
7      00022.png       0.182637     0.173779     0.008858    
8      00020.png       0.202884     0.196027     0.006857    
9      00000.png       0.275658     0.270329     0.005329    
10     00023.png       0.244986     0.239964     0.005021    
11     00021.png       0.224039     0.219102     0.004937    
12     00007.png       0.180567     0.176603     0.003964    
13     00024.png       0.171965     0.168548     0.003417    
14     00016.png       0.209888     0.206706     0.003183    
15     00006.png       0.179148     0.180487     -0.001339   
16     00010.png       0.205853     0.208952     -0.003098   
17     00008.png       0.236806     0.240265     -0.003459   
18     00017.png       0.149850     0.155828     -0.005978   
19     00001.png       0.286398     0.294140     -0.007741   
20     00009.png       0.180549     0.188531     -0.007982   
21     00005.png       0.218832     0.228362     -0.009530   
22     00003.png       0.218665     0.237391     -0.018726   
23     00011.png       0.207409     0.229983     -0.022574   
24     00004.png       0.228138     0.257770     -0.029632   
25     00002.png       0.288538     0.324517     -0.035979   
