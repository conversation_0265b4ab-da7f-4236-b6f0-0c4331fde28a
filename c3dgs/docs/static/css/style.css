@import "https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css";
@import "https://cdn.jsdelivr.net/npm/bulma-carousel@4.0.24/dist/css/bulma-carousel.min.css";


.bal-container-small {
    position: relative;
    width: 100%;
    height: 400px;
    cursor: grab;
    overflow: hidden;
    border-radius: 10px;
    margin: 5px;
}

.bal-container-big {
    position: relative;
    width: 100%;
    height: 400px;
    cursor: grab;
    overflow: hidden;
}

.bal-after {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.bal-before {
    display: block;
    position: absolute;
    top: 0;
    /* right: 0; */
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 15;
    overflow: hidden;
}

.bal-before-inset {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
}

.bal-after img,
.bal-before img {
    object-fit: cover;
    position: absolute;
    width: 100%;
    height: 100%;
    object-position: 50% 50%;
    top: 0;
    bottom: 0;
    left: 0;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
}

.bal-beforePosition {
    background: #121212;
    color: #fff;
    left: 0;
    pointer-events: none;
    border-radius: 0.2rem;
    padding: 2px 10px;
}

.bal-afterPosition {
    background: #121212;
    color: #fff;
    right: 0;
    pointer-events: none;
    border-radius: 0.2rem;
    padding: 2px 10px;
}

.beforeLabel {
    position: absolute;
    bottom: 0;
    margin: 1rem;
    font-size: 1em;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
}

.beforeSize {
    position: absolute;
    top: 0;
    margin: 1rem;
    font-size: 1em;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
    background: white;
    color: rgb(212, 49, 49);
}

.afterSize {
    position: absolute;
    top: 0;
    margin: 1rem;
    font-size: 1em;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
    background: white;
    color: green;
}

.afterLabel {
    position: absolute;
    bottom: 0;
    margin: 1rem;
    font-size: 1em;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
}

.bal-handle {
    height: 41px;
    width: 41px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -20px;
    margin-top: -21px;
    border: 2px solid #fff;
    border-radius: 1000px;
    z-index: 20;
    pointer-events: none;
    box-shadow: 0 0 10px rgb(12, 12, 12);
}

.handle-left-arrow,
.handle-right-arrow {
    width: 0;
    height: 0;
    border: 6px inset transparent;
    position: absolute;
    top: 50%;
    margin-top: -6px;
}

.handle-left-arrow {
    border-right: 6px solid #fff;
    left: 50%;
    margin-left: -17px;
}

.handle-right-arrow {
    border-left: 6px solid #fff;
    right: 50%;
    margin-right: -17px;
}

.bal-handle::before {
    bottom: 50%;
    margin-bottom: 20px;
    box-shadow: 0 0 10px rgb(12, 12, 12);
}

.bal-handle::after {
    top: 50%;
    margin-top: 20.5px;
    box-shadow: 0 0 5px rgb(12, 12, 12);
}

.bal-handle::before,
.bal-handle::after {
    content: " ";
    display: block;
    width: 2px;
    background: #fff;
    height: 9999px;
    position: absolute;
    left: 50%;
    margin-left: -1.5px;
}

.demo-card {
    min-width: 200px;
    min-width: 300px;
    flex-grow: 0;
}

.demo-card .card-image img {
    height: 200px;
    object-fit: cover;

}


.button[disabled]:hover:after {
    content: "Coming soon";
    color: white;
    background-color: black;
    position: absolute;
    border-radius: 5px;
    z-index: 100;
    padding: 0.1rem 0.3rem;
    bottom: -2rem;
}


.results-carousel {
    overflow: hidden;
}

.results-carousel .item {
    margin: 2rem 1rem;
    overflow: hidden;
    border: 1px solid #bbb;
    border-radius: 10px;
    padding: 0;
    font-size: 0;
}

.results-carousel video {
    margin: 0;
}

.publication-links{
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: center;
    gap: 5px;
}