# Copyright (c), ETH Zurich and UNC Chapel Hill.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in the
#       documentation and/or other materials provided with the distribution.
#
#     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
#       its contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
# CO<PERSON>EQ<PERSON><PERSON>IA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.


# Find package config for COLMAP library.
#
# The following variables are set by this config:
#
#   COLMAP_FOUND: TRUE if COLMAP is found.
#   COLMAP_VERSION: COLMAP version.
#
# The colmap::colmap imported interface target is defined.

@PACKAGE_INIT@

set(COLMAP_FOUND FALSE)

# Set hints for finding dependency packages.

set(FREEIMAGE_INCLUDE_DIR_HINTS @FREEIMAGE_INCLUDE_DIR_HINTS@)
set(FREEIMAGE_LIBRARY_DIR_HINTS @FREEIMAGE_LIBRARY_DIR_HINTS@)

set(METIS_INCLUDE_DIR_HINTS @METIS_INCLUDE_DIR_HINTS@)
set(METIS_LIBRARY_DIR_HINTS @METIS_LIBRARY_DIR_HINTS@)

set(GLEW_INCLUDE_DIR_HINTS @GLEW_INCLUDE_DIR_HINTS@)
set(GLEW_LIBRARY_DIR_HINTS @GLEW_LIBRARY_DIR_HINTS@)

set(GLOG_INCLUDE_DIR_HINTS @GLOG_INCLUDE_DIR_HINTS@)
set(GLOG_LIBRARY_DIR_HINTS @GLOG_LIBRARY_DIR_HINTS@)

set(CryptoPP_INCLUDE_DIR_HINTS @CryptoPP_INCLUDE_DIR_HINTS@)
set(CryptoPP_LIBRARY_DIR_HINTS @CryptoPP_LIBRARY_DIR_HINTS@)

# Find dependency packages.

set(TEMP_CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH})
set(CMAKE_MODULE_PATH ${PACKAGE_PREFIX_DIR}/share/colmap/cmake)

# Set the exported variables.

set(COLMAP_FOUND TRUE)

set(COLMAP_VERSION @COLMAP_VERSION@)

set(OPENMP_ENABLED @OPENMP_ENABLED@)

set(CUDA_ENABLED @CUDA_ENABLED@)
set(CUDA_MIN_VERSION @CUDA_MIN_VERSION@)

set(DOWNLOAD_ENABLED @DOWNLOAD_ENABLED@)

set(GUI_ENABLED @GUI_ENABLED@)

set(CGAL_ENABLED @CGAL_ENABLED@)

set(LSD_ENABLED @LSD_ENABLED@)

set(FETCH_POSELIB @FETCH_POSELIB@)

set(FETCH_FAISS @FETCH_FAISS@)

include(${PACKAGE_PREFIX_DIR}/share/colmap/colmap-targets.cmake)
include(${PACKAGE_PREFIX_DIR}/share/colmap/cmake/FindDependencies.cmake)
check_required_components(colmap)

# Reset to previous value
set(CMAKE_MODULE_PATH ${TEMP_CMAKE_MODULE_PATH})
