% Copyright (c), ETH Zurich and UNC Chapel Hill.
% All rights reserved.
%
% Redistribution and use in source and binary forms, with or without
% modification, are permitted provided that the following conditions are met:
%
%     * Redistributions of source code must retain the above copyright
%       notice, this list of conditions and the following disclaimer.
%
%     * Redistributions in binary form must reproduce the above copyright
%       notice, this list of conditions and the following disclaimer in the
%       documentation and/or other materials provided with the distribution.
%
%     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
%       its contributors may be used to endorse or promote products derived
%       from this software without specific prior written permission.
%
% THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
% AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
% IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
% ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
% LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
% CONSEQUE<PERSON>IA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
% SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
% INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
% CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
% ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
% POSSIBILITY OF SUCH DAMAGE.


function write_ply(path, xyz, normals, rgb)
% Write point cloud to PLY text file.

file = fopen(path, 'W');

fprintf(file,'ply\n');
fprintf(file,'format ascii 1.0\n');
fprintf(file,'element vertex %d\n',size(xyz,1));
fprintf(file,'property float x\n');
fprintf(file,'property float y\n');
fprintf(file,'property float z\n');
fprintf(file,'property float nx\n');
fprintf(file,'property float ny\n');
fprintf(file,'property float nz\n');
fprintf(file,'property uchar diffuse_red\n');
fprintf(file,'property uchar diffuse_green\n');
fprintf(file,'property uchar diffuse_blue\n');
fprintf(file,'end_header\n');

for i = 1:size(xyz, 1)
    fprintf(file, '%f %f %f %f %f %f %d %d %d\n', ...
        xyz(i,1), xyz(i,2), xyz(i,3), ...
        normals(i,1), normals(i,2), normals(i,3), ...
        uint8(rgb(i,1)), uint8(rgb(i,2)), uint8(rgb(i,3)));
end

fclose(file);

end
