% Copyright (c), ETH Zurich and UNC Chapel Hill.
% All rights reserved.
%
% Redistribution and use in source and binary forms, with or without
% modification, are permitted provided that the following conditions are met:
%
%     * Redistributions of source code must retain the above copyright
%       notice, this list of conditions and the following disclaimer.
%
%     * Redistributions in binary form must reproduce the above copyright
%       notice, this list of conditions and the following disclaimer in the
%       documentation and/or other materials provided with the distribution.
%
%     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
%       its contributors may be used to endorse or promote products derived
%       from this software without specific prior written permission.
%
% THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
% AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
% IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
% ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
% LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR
% CONSEQUE<PERSON>IA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
% SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
% INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
% CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
% ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
% POSSIBILITY OF SUCH DAMAGE.


function plot_model(cameras, images, points)
% Visualize COLMAP model.

keys = images.keys;
camera_centers = zeros(images.length, 3);
view_dirs = zeros(3 * images.length, 3);
for i = 1:images.length
    image_id = keys{i};
    image = images(image_id);
    camera_centers(i,:) = -image.R' * image.t;
    view_dirs(3 * i - 2,:) = camera_centers(i,:);
    view_dirs(3 * i - 1,:) = camera_centers(i,:)' + image.R' * [0; 0; 0.3];
    view_dirs(3 * i,:) = nan;
end

keys = points.keys;
xyz = zeros(points.length, 3);
for i = 1:points.length
    point_id = keys{i};
    point = points(point_id);
    xyz(i,:) = point.xyz;
end

hold on;
plot3(camera_centers(:,1), camera_centers(:,2), camera_centers(:,3), 'xr');
plot3(view_dirs(:,1), view_dirs(:,2), view_dirs(:,3), '-b');
plot3(xyz(:,1), xyz(:,2), xyz(:,3), '.k');
hold off;

end
