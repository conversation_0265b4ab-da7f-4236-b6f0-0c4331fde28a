name: COLMAP (Ubuntu)

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

on:
  push:
    branches:
      - main
      - release/*
  pull_request:
    types: [ assigned, opened, synchronize, reopened ]
  release:
    types: [ published, edited ]

jobs:
  build:
    name: ${{ matrix.config.os }} ${{ matrix.config.cmakeBuildType }} ${{ matrix.config.cudaEnabled && 'CUDA' || '' }} ${{ matrix.config.asanEnabled && 'ASan' || '' }} ${{ matrix.config.coverageEnabled && 'Coverage' || '' }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      matrix:
        config: [
          {
            os: ubuntu-24.04,
            cmakeBuildType: RelWithDebInfo,
            asanEnabled: false,
            guiEnabled: true,
            cudaEnabled: false,
            e2eTests: false,
            checkCodeFormat: true,
            coverageEnabled: true,
          },
          {
            os: ubuntu-22.04,
            cmakeBuildType: Release,
            asanEnabled: false,
            guiEnabled: true,
            cudaEnabled: false,
            e2eTests: true,
            checkCodeFormat: true,
            coverageEnabled: false,
          },
          {
            os: ubuntu-22.04,
            cmakeBuildType: Release,
            asanEnabled: false,
            guiEnabled: false,
            cudaEnabled: true,
            e2eTests: false,
            checkCodeFormat: false,
            coverageEnabled: false,
          },
          {
            os: ubuntu-24.04,
            cmakeBuildType: Release,
            asanEnabled: true,
            guiEnabled: false,
            cudaEnabled: false,
            e2eTests: false,
            checkCodeFormat: false,
            coverageEnabled: false,
          },
          {
            os: ubuntu-24.04,
            cmakeBuildType: ClangTidy,
            asanEnabled: false,
            guiEnabled: false,
            cudaEnabled: false,
            e2eTests: false,
            checkCodeFormat: false,
            coverageEnabled: false,
          },
        ]

    env:
      COMPILER_CACHE_VERSION: 1
      COMPILER_CACHE_DIR: ${{ github.workspace }}/compiler-cache
      CCACHE_DIR: ${{ github.workspace }}/compiler-cache/ccache
      CCACHE_BASEDIR: ${{ github.workspace }}
      CTCACHE_DIR: ${{ github.workspace }}/compiler-cache/ctcache

    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        id: cache-builds
        with:
          key: v${{ env.COMPILER_CACHE_VERSION }}-${{ matrix.config.os }}-${{ matrix.config.cmakeBuildType }}-${{ matrix.config.asanEnabled }}--${{ matrix.config.cudaEnabled }}-${{ github.run_id }}-${{ github.run_number }}
          restore-keys: v${{ env.COMPILER_CACHE_VERSION }}-${{ matrix.config.os }}-${{ matrix.config.cmakeBuildType }}-${{ matrix.config.asanEnabled }}--${{ matrix.config.cudaEnabled }}
          path: ${{ env.COMPILER_CACHE_DIR }}
      - name: Install compiler cache
        run: |
          mkdir -p "$CCACHE_DIR" "$CTCACHE_DIR"
          echo "$COMPILER_CACHE_DIR/bin" >> $GITHUB_PATH

          if [ -f "$COMPILER_CACHE_DIR/bin/ccache" ]; then
            exit 0
          fi

          set -x
          wget https://github.com/ccache/ccache/releases/download/v4.8.2/ccache-4.8.2-linux-x86_64.tar.xz
          echo "0b33f39766fe9db67f40418aed6a5b3d7b2f4f7fab025a8213264b77a2d0e1b1  ccache-4.8.2-linux-x86_64.tar.xz" | sha256sum --check
          tar xfv ccache-4.8.2-linux-x86_64.tar.xz
          mkdir -p "$COMPILER_CACHE_DIR/bin"
          mv ./ccache-4.8.2-linux-x86_64/ccache "$COMPILER_CACHE_DIR/bin"
          ctcache_commit_id="66c3614175fc650591488519333c411b2eac15a3"
          wget https://github.com/matus-chochlik/ctcache/archive/${ctcache_commit_id}.zip
          echo "108b087f156a9fe7da0c796de1ef73f5855d2a33a27983769ea39061359a40fc  ${ctcache_commit_id}.zip" | sha256sum --check
          unzip "${ctcache_commit_id}.zip"
          mv ctcache-${ctcache_commit_id}/clang-tidy* "$COMPILER_CACHE_DIR/bin"

      - name: Check code format
        if: matrix.config.checkCodeFormat
        run: |
          set +x -euo pipefail
          python -m pip install ruff==0.6.7 clang-format==19.1.0
          ./scripts/format/c++.sh
          ./scripts/format/python.sh
          git diff --name-only
          git diff --exit-code || (echo "Code formatting failed" && exit 1)

      - name: Setup Ubuntu
        run: |
          sudo apt-get update && sudo apt-get install -y \
            build-essential \
            cmake \
            ninja-build \
            libboost-program-options-dev \
            libboost-graph-dev \
            libboost-system-dev \
            libeigen3-dev \
            libceres-dev \
            libfreeimage-dev \
            libmetis-dev \
            libgoogle-glog-dev \
            libgtest-dev \
            libgmock-dev \
            libsqlite3-dev \
            libglew-dev \
            qtbase5-dev \
            libqt5opengl5-dev \
            libcgal-dev \
            libcgal-qt5-dev \
            libgl1-mesa-dri \
            libunwind-dev \
            libcurl4-openssl-dev \
            libopenblas-openmp-dev \
            xvfb

          if [ "${{ matrix.config.cudaEnabled }}" == "true" ]; then
            if [ "${{ matrix.config.os }}" == "ubuntu-20.04" ]; then
              sudo apt-get install -y \
                nvidia-cuda-toolkit \
                nvidia-cuda-toolkit-gcc
              echo "CC=/usr/bin/cuda-gcc" >> $GITHUB_ENV
              echo "CXX=/usr/bin/cuda-g++" >> $GITHUB_ENV
            elif [ "${{ matrix.config.os }}" == "ubuntu-22.04" ]; then
              sudo apt-get install -y \
                nvidia-cuda-toolkit \
                nvidia-cuda-toolkit-gcc \
                gcc-10 g++-10
              echo "CC=/usr/bin/gcc-10" >> $GITHUB_ENV
              echo "CXX=/usr/bin/g++-10" >> $GITHUB_ENV
              echo "CUDAHOSTCXX=/usr/bin/g++-10" >> $GITHUB_ENV
            fi
          fi

          if [ "${{ matrix.config.asanEnabled }}" == "true" ]; then
            sudo apt-get install -y clang-18 libomp-18-dev
            echo "CC=/usr/bin/clang-18" >> $GITHUB_ENV
            echo "CXX=/usr/bin/clang++-18" >> $GITHUB_ENV
          fi

          if [ "${{ matrix.config.cmakeBuildType }}" == "ClangTidy" ]; then
            sudo apt-get install -y clang-18 clang-tidy-18 libomp-18-dev
            echo "CC=/usr/bin/clang-18" >> $GITHUB_ENV
            echo "CXX=/usr/bin/clang++-18" >> $GITHUB_ENV
          fi

          if [ "${{ matrix.config.coverageEnabled }}" == "true" ]; then
            sudo apt-get install -y gcovr
          fi

      - name: Configure and build
        run: |
          set -x
          cmake --version
          mkdir build
          cd build
          cmake .. \
            -GNinja \
            -DCMAKE_BUILD_TYPE=${{ matrix.config.cmakeBuildType }} \
            -DCMAKE_INSTALL_PREFIX=./install \
            -DCMAKE_CUDA_ARCHITECTURES=50 \
            -DTESTS_ENABLED=ON \
            -DCUDA_ENABLED=${{ matrix.config.cudaEnabled }} \
            -DGUI_ENABLED=${{ matrix.config.guiEnabled }} \
            -DASAN_ENABLED=${{ matrix.config.asanEnabled }} \
            -DCOVERAGE_ENABLED=${{ matrix.config.coverageEnabled }}
          ninja -k 10000

      - name: Install and build sample
        if: ${{ matrix.config.cmakeBuildType != 'ClangTidy' && !matrix.config.asanEnabled && !matrix.config.coverageEnabled }}
        run: |
          set -x
          cd build
          ninja install
          cd ../doc/sample-project
          mkdir build
          cd build
          export colmap_DIR=${{ github.workspace }}/build/install/share/colmap
          cmake .. \
            -GNinja \
            -DCMAKE_CUDA_ARCHITECTURES=50
          ninja
          ./hello_world --message "world"

      - name: Run tests
        if: ${{ matrix.config.cmakeBuildType != 'ClangTidy' }}
        run: |
          if [ "${{ matrix.config.cudaEnabled }}" == "true" ]; then
            ctestExclusions="(feature/colmap_feature_sift_test)|(mvs/colmap_mvs_gpu_mat_test)"
          fi

          export DISPLAY=":99.0"
          export QT_QPA_PLATFORM="offscreen"
          Xvfb :99 &
          sleep 3
          cd build
          export GLOG_v=1
          export GLOG_logtostderr=1
          ctest -E "$ctestExclusions" --output-on-failure

      - name: Run E2E tests
        if: matrix.config.e2eTests
        run: |
          export DISPLAY=":99.0"
          export QT_QPA_PLATFORM="offscreen"
          Xvfb :99 &
          sleep 3
          sudo apt install 7zip
          mkdir eth3d_benchmark
          # Error thresholds in degrees and meters,
          # as the ETH3D groundtruth has metric scale.
          GLOG_v=1 python ./scripts/python/benchmark_eth3d.py \
              --workspace_path ./eth3d_benchmark \
              --colmap_path ./build/src/colmap/exe/colmap \
              --dataset_names boulders,door \
              --max_rotation_error 1.0 \
              --max_proj_center_error 0.05

      - name: Generate coverage report
        if: matrix.config.coverageEnabled
        run: |
          set -x
          cd build
          ../scripts/shell/generate_coverage_report.sh

      - name: Upload coverage HTML report
        uses: actions/upload-artifact@v4
        if: matrix.config.coverageEnabled
        with:
          name: code-coverage
          path: build/coverage-html

      - name: Generate Github code coverage report
        if: matrix.config.coverageEnabled
        uses: irongut/CodeCoverageSummary@v1.3.0
        with:
          filename: build/coverage-cobertura.xml
          badge: true
          fail_below_min: false
          format: markdown
          hide_branch_rate: false
          hide_complexity: true
          indicators: true
          output: both
          thresholds: '75 90'
      # TODO: Add code coverage comment to PR. Currently, this action reports
      # coverage for the entire repository, not just the changed files in the PR.
      # We could manually filter the coverage report to only include the changed
      # files in the PR, but this is non-trivial and may not be worth the effort.
      # - name: Add Github PR code coverage comment
      #   uses: marocchino/sticky-pull-request-comment@v2
      #   if: ${{ matrix.config.coverageEnabled && github.event_name == 'pull_request' }}
      #   with:
      #     recreate: true
      #     path: code-coverage-results.md

      - name: Cleanup compiler cache
        run: |
          set -x
          ccache --show-stats --verbose
          ccache --evict-older-than 1d
          ccache --show-stats --verbose

          echo "Size of ctcache before: $(du -sh $CTCACHE_DIR)"
          echo "Number of ctcache files before: $(find $CTCACHE_DIR | wc -l)"
          # Delete cache older than 10 days.
          find "$CTCACHE_DIR"/*/ -mtime +10 -print0 | xargs -0 rm -rf
          echo "Size of ctcache after:  $(du -sh $CTCACHE_DIR)"
          echo "Number of ctcache files after: $(find $CTCACHE_DIR | wc -l)"
