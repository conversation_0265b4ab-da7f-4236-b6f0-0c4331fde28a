name: COLM<PERSON> (Docker)

on:
  push:
    branches:
      - main
      - release/*
  pull_request:
    types: [ assigned, opened, synchronize, reopened ]
  release:
    types: [ published, edited ]

env:
  IS_RELEASE: ${{ github.event_name == 'release' || startsWith(github.ref, 'refs/tags') }}

jobs:
  build:
    name: ubuntu-24.04
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        if: ${{ env.IS_RELEASE == 'true' }}
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        run: |
          dockertag=$(date +%Y%m%d).${{ github.run_number }}
          if [[ "${{ env.IS_RELEASE }}" == "true" ]]; then
            cuda_archs="50;60;70;75;90"
          else
            cuda_archs="50"
          fi
          if [ "$GITHUB_EVENT_NAME" == "pull_request" ]; then
            GITHUB_SHA=$(cat $GITHUB_EVENT_PATH | jq -r .pull_request.head.sha)
          fi
          docker build . \
            --file docker/Dockerfile \
            --tag colmap/colmap:$dockertag \
            --build-arg COLMAP_GIT_COMMIT="$GITHUB_SHA" \
            --build-arg CUDA_ARCHITECTURES="$cuda_archs"
          if [[ "${{ env.IS_RELEASE }}" == "true" ]]; then
            docker tag colmap/colmap:$dockertag colmap/colmap:latest
            docker push colmap/colmap:$dockertag
            docker push colmap/colmap:latest
          fi
