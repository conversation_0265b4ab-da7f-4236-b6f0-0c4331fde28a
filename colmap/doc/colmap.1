.TH colmap 1 "January 4 2018"
.SH NAME
colmap \- Structure-from-Motion and Multi-View Stereo
.SH SYNOPSIS
.B colmap
.RI "[command] [options]"
.SH DESCRIPTION
This manual page documents briefly the
.B colmap
command.
.PP
COLMAP is a general-purpose Structure-from-Motion (SfM) and Multi-View
Stereo (MVS) pipeline with a graphical and command-line interface. It offers
a wide range of features for reconstruction of ordered and unordered image
collections.
.SH OPTIONS
This program offers a graphical and command-line interface. Each command
offers summary of all available options.
.TP
.B help [ \-h, \-\-help ]
Show summary of all options.
.TP
.B gui
Start graphical interface.
.TP
.B gui \-h [ \-\-help ]
Show summary of graphical interface options.
.TP
.B feature_extractor \-h [ \-\-help ]
Show summary of feature extractor options.
.TP
.B feature_extractor \-\-image_path IMAGES \-\-database_path DATABASE
Extract features for images in the given folder and store them in the database.
.br
.TP
.B ...
.br
.SH ONLINE DOCUMENTATION
The program is documented at https://colmap.github.io/
