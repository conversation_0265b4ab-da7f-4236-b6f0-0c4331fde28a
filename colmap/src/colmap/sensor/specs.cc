// Copyright (c), ETH Zurich and UNC Chapel Hill.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//
//     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
//       its contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

#include "colmap/sensor/specs.h"

namespace colmap {

camera_specs_t InitializeCameraSpecs() {
  camera_specs_t specs;

  {
    auto& make_specs = specs["acer"];
    make_specs.reserve(17);
    make_specs.emplace_back("ce5330", 5.7500f);
    make_specs.emplace_back("ce5430", 5.7500f);
    make_specs.emplace_back("ce6430", 5.7500f);
    make_specs.emplace_back("ci6330", 7.1100f);
    make_specs.emplace_back("ci6530", 7.1100f);
    make_specs.emplace_back("ci8330", 7.1100f);
    make_specs.emplace_back("cl5300", 5.7500f);
    make_specs.emplace_back("cp8531", 7.1100f);
    make_specs.emplace_back("cp8660", 7.1100f);
    make_specs.emplace_back("cr5130", 7.1100f);
    make_specs.emplace_back("cr6530", 7.1100f);
    make_specs.emplace_back("cr8530", 7.1100f);
    make_specs.emplace_back("cs5530", 5.7500f);
    make_specs.emplace_back("cs5531", 5.7500f);
    make_specs.emplace_back("cs6530", 5.7500f);
    make_specs.emplace_back("cs6531", 5.7500f);
    make_specs.emplace_back("cu6530", 5.7500f);
  }

  {
    auto& make_specs = specs["agfaphoto"];
    make_specs.reserve(53);
    make_specs.emplace_back("compact100", 5.7500f);
    make_specs.emplace_back("compact102", 6.1600f);
    make_specs.emplace_back("compact103", 6.0800f);
    make_specs.emplace_back("dc1030i", 7.1100f);
    make_specs.emplace_back("dc1033m", 5.7500f);
    make_specs.emplace_back("dc1033x", 7.1100f);
    make_specs.emplace_back("dc1338i", 7.1100f);
    make_specs.emplace_back("dc1338st", 7.1100f);
    make_specs.emplace_back("dc2030m", 6.1600f);
    make_specs.emplace_back("dc302", 4.8000f);
    make_specs.emplace_back("dc500", 4.8000f);
    make_specs.emplace_back("dc530i", 5.7500f);
    make_specs.emplace_back("dc533", 5.7500f);
    make_specs.emplace_back("dc600uw", 6.1600f);
    make_specs.emplace_back("dc630i", 5.7500f);
    make_specs.emplace_back("dc630x", 5.7500f);
    make_specs.emplace_back("dc630", 5.7500f);
    make_specs.emplace_back("dc633xs", 5.7500f);
    make_specs.emplace_back("dc633x", 5.7500f);
    make_specs.emplace_back("dc730i", 5.7500f);
    make_specs.emplace_back("dc733i", 5.7500f);
    make_specs.emplace_back("dc733s", 5.7500f);
    make_specs.emplace_back("dc735i", 5.7500f);
    make_specs.emplace_back("dc735", 5.7500f);
    make_specs.emplace_back("dc738i", 5.7500f);
    make_specs.emplace_back("dc830i", 7.1100f);
    make_specs.emplace_back("dc830", 5.7500f);
    make_specs.emplace_back("dc8330i", 5.7500f);
    make_specs.emplace_back("dc8338i", 5.7500f);
    make_specs.emplace_back("dc833m", 5.7500f);
    make_specs.emplace_back("dc8428s", 7.1100f);
    make_specs.emplace_back("ephoto1280", 6.1600f);
    make_specs.emplace_back("ephoto1680", 6.1600f);
    make_specs.emplace_back("ephotocl18", 6.1600f);
    make_specs.emplace_back("ephotocl30clik!", 6.1600f);
    make_specs.emplace_back("ephotocl30", 6.1600f);
    make_specs.emplace_back("ephotocl45", 6.1600f);
    make_specs.emplace_back("ephotocl50", 6.1600f);
    make_specs.emplace_back("optima100", 6.0800f);
    make_specs.emplace_back("optima102", 6.0800f);
    make_specs.emplace_back("optima103", 6.1600f);
    make_specs.emplace_back("optima104", 6.0800f);
    make_specs.emplace_back("optima105", 6.1600f);
    make_specs.emplace_back("optima1338mt", 6.1600f);
    make_specs.emplace_back("optima1438m", 6.0800f);
    make_specs.emplace_back("optima1", 6.0800f);
    make_specs.emplace_back("optima3", 6.1600f);
    make_specs.emplace_back("optima830uw", 5.7500f);
    make_specs.emplace_back("optima8328m", 5.7500f);
    make_specs.emplace_back("sensor505d", 5.7500f);
    make_specs.emplace_back("sensor505x", 5.7500f);
    make_specs.emplace_back("sensor530s", 5.7500f);
    make_specs.emplace_back("sensor830s", 5.7500f);
  }

  {
    auto& make_specs = specs["benq"];
    make_specs.reserve(107);
    make_specs.emplace_back("ac100", 6.1600f);
    make_specs.emplace_back("ae100", 6.1600f);
    make_specs.emplace_back("c1420", 6.0800f);
    make_specs.emplace_back("dc2300", 4.5000f);
    make_specs.emplace_back("dc2410", 5.3300f);
    make_specs.emplace_back("dc3400", 4.5000f);
    make_specs.emplace_back("dc3410", 4.5000f);
    make_specs.emplace_back("dc4330", 5.7500f);
    make_specs.emplace_back("dc4500", 5.7500f);
    make_specs.emplace_back("dc5330", 5.7500f);
    make_specs.emplace_back("dcc1000", 7.1100f);
    make_specs.emplace_back("dcc1020", 6.1600f);
    make_specs.emplace_back("dcc1030eco", 6.1600f);
    make_specs.emplace_back("dcc1035", 6.1600f);
    make_specs.emplace_back("dcc1050", 7.5300f);
    make_specs.emplace_back("dcc1060", 6.1600f);
    make_specs.emplace_back("dcc1220", 6.1600f);
    make_specs.emplace_back("dcc1230", 6.1600f);
    make_specs.emplace_back("dcc1250", 6.1600f);
    make_specs.emplace_back("dcc1255", 6.1600f);
    make_specs.emplace_back("dcc1430", 6.1600f);
    make_specs.emplace_back("dcc1450", 6.1600f);
    make_specs.emplace_back("dcc1460", 6.0800f);
    make_specs.emplace_back("dcc1480", 6.1600f);
    make_specs.emplace_back("dcc30", 5.7500f);
    make_specs.emplace_back("dcc35", 5.7500f);
    make_specs.emplace_back("dcc40", 5.7500f);
    make_specs.emplace_back("dcc420", 5.7500f);
    make_specs.emplace_back("dcc500", 5.7500f);
    make_specs.emplace_back("dcc50", 7.1100f);
    make_specs.emplace_back("dcc510", 5.7500f);
    make_specs.emplace_back("dcc51", 5.7500f);
    make_specs.emplace_back("dcc520", 5.7500f);
    make_specs.emplace_back("dcc530", 5.7500f);
    make_specs.emplace_back("dcc540", 5.7500f);
    make_specs.emplace_back("dcc60", 7.1100f);
    make_specs.emplace_back("dcc610", 5.7500f);
    make_specs.emplace_back("dcc62", 7.1100f);
    make_specs.emplace_back("dcc630", 5.7500f);
    make_specs.emplace_back("dcc640", 7.5300f);
    make_specs.emplace_back("dcc740i", 5.7500f);
    make_specs.emplace_back("dcc740", 5.7500f);
    make_specs.emplace_back("dcc750", 5.7500f);
    make_specs.emplace_back("dcc800", 7.1100f);
    make_specs.emplace_back("dcc840", 5.7500f);
    make_specs.emplace_back("dcc850", 5.7500f);
    make_specs.emplace_back("dce1000", 7.1100f);
    make_specs.emplace_back("dce1020", 6.1600f);
    make_specs.emplace_back("dce1030", 6.1600f);
    make_specs.emplace_back("dce1035", 6.1600f);
    make_specs.emplace_back("dce1050t", 6.1600f);
    make_specs.emplace_back("dce1050", 7.5300f);
    make_specs.emplace_back("dce1220", 6.1600f);
    make_specs.emplace_back("dce1230", 6.1600f);
    make_specs.emplace_back("dce1240", 6.1600f);
    make_specs.emplace_back("dce1250", 6.1600f);
    make_specs.emplace_back("dce1260", 6.1600f);
    make_specs.emplace_back("dce1280", 6.1600f);
    make_specs.emplace_back("dce1420", 6.1600f);
    make_specs.emplace_back("dce1430", 6.1600f);
    make_specs.emplace_back("dce1460", 6.1600f);
    make_specs.emplace_back("dce1465", 6.1600f);
    make_specs.emplace_back("dce300", 6.4000f);
    make_specs.emplace_back("dce30", 6.4000f);
    make_specs.emplace_back("dce310", 6.4000f);
    make_specs.emplace_back("dce40", 5.7500f);
    make_specs.emplace_back("dce41", 5.7500f);
    make_specs.emplace_back("dce43", 5.7500f);
    make_specs.emplace_back("dce510", 5.7500f);
    make_specs.emplace_back("dce520plus", 5.7500f);
    make_specs.emplace_back("dce520", 5.7500f);
    make_specs.emplace_back("dce53", 5.7500f);
    make_specs.emplace_back("dce600", 5.7500f);
    make_specs.emplace_back("dce605", 5.7500f);
    make_specs.emplace_back("dce610", 5.7500f);
    make_specs.emplace_back("dce63plus", 5.7500f);
    make_specs.emplace_back("dce720", 5.7500f);
    make_specs.emplace_back("dce800", 5.7500f);
    make_specs.emplace_back("dce820", 5.7500f);
    make_specs.emplace_back("dcl1020", 6.1600f);
    make_specs.emplace_back("dcp1410", 6.1600f);
    make_specs.emplace_back("dcp500", 5.7500f);
    make_specs.emplace_back("dcp860", 7.1100f);
    make_specs.emplace_back("dcs1430", 6.0800f);
    make_specs.emplace_back("dcs30", 5.3300f);
    make_specs.emplace_back("dcs40", 5.7500f);
    make_specs.emplace_back("dct1260", 6.1600f);
    make_specs.emplace_back("dct700", 5.7500f);
    make_specs.emplace_back("dct800", 6.0300f);
    make_specs.emplace_back("dct850", 5.7500f);
    make_specs.emplace_back("dcw1220", 6.1600f);
    make_specs.emplace_back("dcx600", 5.7500f);
    make_specs.emplace_back("dcx710", 5.7500f);
    make_specs.emplace_back("dcx720", 5.7500f);
    make_specs.emplace_back("dcx725", 5.7500f);
    make_specs.emplace_back("dcx735", 5.7500f);
    make_specs.emplace_back("dcx800", 5.7500f);
    make_specs.emplace_back("dcx835", 5.7500f);
    make_specs.emplace_back("e1480", 6.1600f);
    make_specs.emplace_back("g1", 6.1600f);
    make_specs.emplace_back("gh200", 6.1600f);
    make_specs.emplace_back("gh600", 6.1600f);
    make_specs.emplace_back("gh700", 6.1600f);
    make_specs.emplace_back("lm100", 6.1600f);
    make_specs.emplace_back("s1410", 6.0800f);
    make_specs.emplace_back("s1420", 6.1600f);
    make_specs.emplace_back("t1460", 6.1600f);
  }

  {
    auto& make_specs = specs["canon"];
    make_specs.reserve(314);
    make_specs.emplace_back("digitalixus100is", 6.1600f);
    make_specs.emplace_back("digitalixus110is", 6.1600f);
    make_specs.emplace_back("digitalixus200is", 6.1600f);
    make_specs.emplace_back("digitalixus300", 5.3300f);
    make_specs.emplace_back("digitalixus330", 5.3300f);
    make_specs.emplace_back("digitalixus400", 7.1100f);
    make_specs.emplace_back("digitalixus40", 5.7500f);
    make_specs.emplace_back("digitalixus430", 7.1100f);
    make_specs.emplace_back("digitalixus500", 7.1100f);
    make_specs.emplace_back("digitalixus50", 5.7500f);
    make_specs.emplace_back("digitalixus60", 5.7500f);
    make_specs.emplace_back("digitalixus65", 5.7500f);
    make_specs.emplace_back("digitalixus80is", 5.7500f);
    make_specs.emplace_back("digitalixus800is", 5.7500f);
    make_specs.emplace_back("digitalixus85is", 6.1600f);
    make_specs.emplace_back("digitalixus850is", 5.7500f);
    make_specs.emplace_back("digitalixus860is", 5.7500f);
    make_specs.emplace_back("digitalixus870is", 6.1600f);
    make_specs.emplace_back("digitalixus90is", 6.1600f);
    make_specs.emplace_back("digitalixus900ti", 7.1100f);
    make_specs.emplace_back("digitalixus95is", 6.1600f);
    make_specs.emplace_back("digitalixus950is", 5.7500f);
    make_specs.emplace_back("digitalixus960is", 7.5300f);
    make_specs.emplace_back("digitalixus970is", 6.1600f);
    make_specs.emplace_back("digitalixus980is", 7.5300f);
    make_specs.emplace_back("digitalixus990is", 6.1600f);
    make_specs.emplace_back("digitalixusizoom", 5.7500f);
    make_specs.emplace_back("digitalixusi7", 5.7500f);
    make_specs.emplace_back("digitalixusiis", 5.3300f);
    make_specs.emplace_back("digitalixusii", 5.3300f);
    make_specs.emplace_back("digitalixusi", 5.7500f);
    make_specs.emplace_back("digitalixusv2", 5.3300f);
    make_specs.emplace_back("digitalixusv3", 5.3300f);
    make_specs.emplace_back("digitalixusv", 5.3300f);
    make_specs.emplace_back("digitalixus", 5.3300f);
    make_specs.emplace_back("elph135/ixus145", 6.1600f);
    make_specs.emplace_back("elph140is/ixus150", 6.1600f);
    make_specs.emplace_back("elph150is/ixus155", 6.1600f);
    make_specs.emplace_back("elph160/ixus160", 6.1600f);
    make_specs.emplace_back("elph170is/ixus170", 6.1600f);
    make_specs.emplace_back("eos1000d", 22.2000f);
    make_specs.emplace_back("eos10d", 22.7000f);
    make_specs.emplace_back("eos1dc", 36.0000f);
    make_specs.emplace_back("eos1dmarkiin", 28.7000f);
    make_specs.emplace_back("eos1dmarkiii", 28.7000f);
    make_specs.emplace_back("eos1dmarkii", 28.7000f);
    make_specs.emplace_back("eos1dmarkiv", 27.9000f);
    make_specs.emplace_back("eos1dx", 36.0000f);
    make_specs.emplace_back("eos1dsmarkiii", 36.0000f);
    make_specs.emplace_back("eos1dsmarkii", 36.0000f);
    make_specs.emplace_back("eos1ds", 35.8000f);
    make_specs.emplace_back("eos1d", 28.7000f);
    make_specs.emplace_back("eos20da", 22.5000f);
    make_specs.emplace_back("eos20d", 22.5000f);
    make_specs.emplace_back("eos300d", 22.7000f);
    make_specs.emplace_back("eos30d", 22.5000f);
    make_specs.emplace_back("eos350d", 22.2000f);
    make_specs.emplace_back("eos400d", 22.2000f);
    make_specs.emplace_back("eos40d", 22.2000f);
    make_specs.emplace_back("eos450d", 22.2000f);
    make_specs.emplace_back("eos500d", 22.3000f);
    make_specs.emplace_back("eos50d", 22.3000f);
    make_specs.emplace_back("eos5dmarkiii", 36.0000f);
    make_specs.emplace_back("eos5dmarkii", 36.0000f);
    make_specs.emplace_back("eos5dsr", 36.0000f);
    make_specs.emplace_back("eos5ds", 36.0000f);
    make_specs.emplace_back("eos5d", 35.8000f);
    make_specs.emplace_back("eos60da", 22.3000f);
    make_specs.emplace_back("eos60d", 22.3000f);
    make_specs.emplace_back("eos6d", 35.8000f);
    make_specs.emplace_back("eos70d", 22.5000f);
    make_specs.emplace_back("eos7dmarkii", 22.4000f);
    make_specs.emplace_back("eos7d", 22.3000f);
    make_specs.emplace_back("eosd30", 22.7000f);
    make_specs.emplace_back("eosd60", 22.7000f);
    make_specs.emplace_back("eosm10", 22.3000f);
    make_specs.emplace_back("eosm3", 22.3000f);
    make_specs.emplace_back("eosm", 22.3000f);
    make_specs.emplace_back("eosrebelsl1/100d", 22.3000f);
    make_specs.emplace_back("eosrebelt2i/550d", 22.3000f);
    make_specs.emplace_back("eosrebelt3i/600d", 22.3000f);
    make_specs.emplace_back("eosrebelt3/1100d", 22.2000f);
    make_specs.emplace_back("eosrebelt4i/650d", 22.3000f);
    make_specs.emplace_back("eosrebelt5i/700d", 22.3000f);
    make_specs.emplace_back("eosrebelt5/1200d", 22.3000f);
    make_specs.emplace_back("eosrebelt6i/750d", 22.3000f);
    make_specs.emplace_back("eosrebelt6s/760d", 22.3000f);
    make_specs.emplace_back("ixus1000hs", 6.1600f);
    make_specs.emplace_back("ixus105", 6.1600f);
    make_specs.emplace_back("ixus1100hs", 6.1600f);
    make_specs.emplace_back("ixus115hs", 6.1600f);
    make_specs.emplace_back("ixus125hs", 6.1600f);
    make_specs.emplace_back("ixus130", 6.1600f);
    make_specs.emplace_back("ixus132", 6.1600f);
    make_specs.emplace_back("ixus165", 6.1600f);
    make_specs.emplace_back("ixus210", 6.1600f);
    make_specs.emplace_back("ixus220hs", 6.1600f);
    make_specs.emplace_back("ixus230hs", 6.1600f);
    make_specs.emplace_back("ixus240hs", 6.1600f);
    make_specs.emplace_back("ixus300hs", 6.1600f);
    make_specs.emplace_back("ixus310hs", 6.1600f);
    make_specs.emplace_back("ixus500hs", 6.1600f);
    make_specs.emplace_back("ixus510hs", 6.1600f);
    make_specs.emplace_back("powershot350", 4.8000f);
    make_specs.emplace_back("powershot600", 4.8000f);
    make_specs.emplace_back("powershota1000is", 6.1600f);
    make_specs.emplace_back("powershota100", 4.5000f);
    make_specs.emplace_back("powershota10", 5.3300f);
    make_specs.emplace_back("powershota1100is", 6.1600f);
    make_specs.emplace_back("powershota1200", 6.1600f);
    make_specs.emplace_back("powershota1300", 6.1600f);
    make_specs.emplace_back("powershota1400", 6.1600f);
    make_specs.emplace_back("powershota2000is", 6.1600f);
    make_specs.emplace_back("powershota200", 4.5000f);
    make_specs.emplace_back("powershota20", 5.3300f);
    make_specs.emplace_back("powershota2100is", 6.1600f);
    make_specs.emplace_back("powershota2200", 6.1600f);
    make_specs.emplace_back("powershota2300", 6.1600f);
    make_specs.emplace_back("powershota2400is", 6.1600f);
    make_specs.emplace_back("powershota2500", 6.1600f);
    make_specs.emplace_back("powershota2600", 6.1600f);
    make_specs.emplace_back("powershota3000is", 6.1600f);
    make_specs.emplace_back("powershota300", 5.3300f);
    make_specs.emplace_back("powershota30", 5.3300f);
    make_specs.emplace_back("powershota3100is", 6.1600f);
    make_specs.emplace_back("powershota310", 5.3300f);
    make_specs.emplace_back("powershota3200is", 6.1600f);
    make_specs.emplace_back("powershota3300is", 6.1600f);
    make_specs.emplace_back("powershota3400is", 6.1600f);
    make_specs.emplace_back("powershota3500is", 6.1600f);
    make_specs.emplace_back("powershota4000is", 6.1600f);
    make_specs.emplace_back("powershota400", 4.5000f);
    make_specs.emplace_back("powershota40", 5.3300f);
    make_specs.emplace_back("powershota410", 4.5000f);
    make_specs.emplace_back("powershota420", 4.8000f);
    make_specs.emplace_back("powershota430", 4.8000f);
    make_specs.emplace_back("powershota450", 4.8000f);
    make_specs.emplace_back("powershota460", 4.8000f);
    make_specs.emplace_back("powershota470", 5.7500f);
    make_specs.emplace_back("powershota480", 6.1600f);
    make_specs.emplace_back("powershota490", 6.1600f);
    make_specs.emplace_back("powershota495", 6.1600f);
    make_specs.emplace_back("powershota5zoom", 4.8000f);
    make_specs.emplace_back("powershota50", 4.8000f);
    make_specs.emplace_back("powershota510", 5.3300f);
    make_specs.emplace_back("powershota520", 5.7500f);
    make_specs.emplace_back("powershota530", 5.7500f);
    make_specs.emplace_back("powershota540", 5.7500f);
    make_specs.emplace_back("powershota550", 5.7500f);
    make_specs.emplace_back("powershota560", 5.7500f);
    make_specs.emplace_back("powershota570is", 5.7500f);
    make_specs.emplace_back("powershota580", 5.7500f);
    make_specs.emplace_back("powershota590is", 5.7500f);
    make_specs.emplace_back("powershota5", 4.8000f);
    make_specs.emplace_back("powershota60", 5.3300f);
    make_specs.emplace_back("powershota610", 7.1100f);
    make_specs.emplace_back("powershota620", 7.1100f);
    make_specs.emplace_back("powershota630", 7.1100f);
    make_specs.emplace_back("powershota640", 7.1100f);
    make_specs.emplace_back("powershota650is", 7.5300f);
    make_specs.emplace_back("powershota700", 5.7500f);
    make_specs.emplace_back("powershota70", 5.3300f);
    make_specs.emplace_back("powershota710is", 5.7500f);
    make_specs.emplace_back("powershota720is", 5.7500f);
    make_specs.emplace_back("powershota75", 5.3300f);
    make_specs.emplace_back("powershota800", 6.1600f);
    make_specs.emplace_back("powershota80", 7.1100f);
    make_specs.emplace_back("powershota810", 6.1600f);
    make_specs.emplace_back("powershota85", 5.3300f);
    make_specs.emplace_back("powershota95", 7.1100f);
    make_specs.emplace_back("powershotd10", 6.1600f);
    make_specs.emplace_back("powershotd20", 6.1600f);
    make_specs.emplace_back("powershotd30", 6.1600f);
    make_specs.emplace_back("powershote1", 6.1600f);
    make_specs.emplace_back("powershotelph100hs", 6.1600f);
    make_specs.emplace_back("powershotelph110hs", 6.1600f);
    make_specs.emplace_back("powershotelph115is", 6.1600f);
    make_specs.emplace_back("powershotelph130is", 6.1600f);
    make_specs.emplace_back("powershotelph300hs", 6.1600f);
    make_specs.emplace_back("powershotelph310hs", 6.1600f);
    make_specs.emplace_back("powershotelph320hs", 6.1600f);
    make_specs.emplace_back("powershotelph330hs", 6.1600f);
    make_specs.emplace_back("powershotelph340hs", 6.1600f);
    make_specs.emplace_back("powershotelph350hs", 6.1600f);
    make_specs.emplace_back("powershotelph500hs", 6.1600f);
    make_specs.emplace_back("powershotelph510hs", 6.1600f);
    make_specs.emplace_back("powershotelph520hs", 6.1600f);
    make_specs.emplace_back("powershotelph530hs", 6.1600f);
    make_specs.emplace_back("powershotg1xmarkii", 18.7000f);
    make_specs.emplace_back("powershotg1x", 18.7000f);
    make_specs.emplace_back("powershotg10", 7.5300f);
    make_specs.emplace_back("powershotg11", 7.5300f);
    make_specs.emplace_back("powershotg12", 7.5300f);
    make_specs.emplace_back("powershotg15", 7.5300f);
    make_specs.emplace_back("powershotg16", 7.5300f);
    make_specs.emplace_back("powershotg1", 7.1100f);
    make_specs.emplace_back("powershotg2", 7.1100f);
    make_specs.emplace_back("powershotg3x", 13.2000f);
    make_specs.emplace_back("powershotg3", 7.1100f);
    make_specs.emplace_back("powershotg5x", 13.2000f);
    make_specs.emplace_back("powershotg5", 7.1100f);
    make_specs.emplace_back("powershotg6", 7.1100f);
    make_specs.emplace_back("powershotg7x", 13.2000f);
    make_specs.emplace_back("powershotg7", 7.1100f);
    make_specs.emplace_back("powershotg9x", 13.2000f);
    make_specs.emplace_back("powershotg9", 7.5300f);
    make_specs.emplace_back("powershotn100", 7.5300f);
    make_specs.emplace_back("powershotn2", 6.1600f);
    make_specs.emplace_back("powershotn", 6.1600f);
    make_specs.emplace_back("powershotpro1", 8.8000f);
    make_specs.emplace_back("powershotpro70", 6.4000f);
    make_specs.emplace_back("powershotpro90is", 7.1100f);
    make_specs.emplace_back("powershots1is", 5.3300f);
    make_specs.emplace_back("powershots100digitalixus", 5.3300f);
    make_specs.emplace_back("powershots100", 7.5300f);
    make_specs.emplace_back("powershots10", 6.4000f);
    make_specs.emplace_back("powershots110", 7.5300f);
    make_specs.emplace_back("powershots120", 7.5300f);
    make_specs.emplace_back("powershots2is", 5.7500f);
    make_specs.emplace_back("powershots200", 5.3300f);
    make_specs.emplace_back("powershots20", 7.1100f);
    make_specs.emplace_back("powershots230", 5.3300f);
    make_specs.emplace_back("powershots3is", 5.7500f);
    make_specs.emplace_back("powershots300", 5.3300f);
    make_specs.emplace_back("powershots30", 7.1100f);
    make_specs.emplace_back("powershots330", 5.3300f);
    make_specs.emplace_back("powershots400", 7.1100f);
    make_specs.emplace_back("powershots40", 7.1100f);
    make_specs.emplace_back("powershots410", 7.1100f);
    make_specs.emplace_back("powershots45", 7.1100f);
    make_specs.emplace_back("powershots5is", 5.7500f);
    make_specs.emplace_back("powershots500", 7.1100f);
    make_specs.emplace_back("powershots50", 7.1100f);
    make_specs.emplace_back("powershots60", 7.1100f);
    make_specs.emplace_back("powershots70", 7.1100f);
    make_specs.emplace_back("powershots80", 7.1100f);
    make_specs.emplace_back("powershots90", 7.5300f);
    make_specs.emplace_back("powershots95", 7.5300f);
    make_specs.emplace_back("powershotsd1000", 5.7500f);
    make_specs.emplace_back("powershotsd100", 5.3300f);
    make_specs.emplace_back("powershotsd10", 5.7500f);
    make_specs.emplace_back("powershotsd1100is", 5.7500f);
    make_specs.emplace_back("powershotsd110", 5.3300f);
    make_specs.emplace_back("powershotsd1200is", 6.1600f);
    make_specs.emplace_back("powershotsd1300is", 6.1600f);
    make_specs.emplace_back("powershotsd1400is", 6.1600f);
    make_specs.emplace_back("powershotsd200", 5.7500f);
    make_specs.emplace_back("powershotsd20", 5.7500f);
    make_specs.emplace_back("powershotsd300", 5.7500f);
    make_specs.emplace_back("powershotsd30", 5.7500f);
    make_specs.emplace_back("powershotsd3500is", 6.1600f);
    make_specs.emplace_back("powershotsd4000is", 6.1600f);
    make_specs.emplace_back("powershotsd400", 5.7500f);
    make_specs.emplace_back("powershotsd40", 5.7500f);
    make_specs.emplace_back("powershotsd430wireless", 5.7500f);
    make_specs.emplace_back("powershotsd4500is", 6.1600f);
    make_specs.emplace_back("powershotsd450", 5.7500f);
    make_specs.emplace_back("powershotsd500", 7.1100f);
    make_specs.emplace_back("powershotsd550", 7.1100f);
    make_specs.emplace_back("powershotsd600", 5.7500f);
    make_specs.emplace_back("powershotsd630", 5.7500f);
    make_specs.emplace_back("powershotsd700is", 5.7500f);
    make_specs.emplace_back("powershotsd750", 5.7500f);
    make_specs.emplace_back("powershotsd770is", 6.1600f);
    make_specs.emplace_back("powershotsd780is", 6.1600f);
    make_specs.emplace_back("powershotsd790is", 6.1600f);
    make_specs.emplace_back("powershotsd800is", 5.7500f);
    make_specs.emplace_back("powershotsd850is", 5.7500f);
    make_specs.emplace_back("powershotsd870is", 5.7500f);
    make_specs.emplace_back("powershotsd880is", 6.1600f);
    make_specs.emplace_back("powershotsd890is", 6.1600f);
    make_specs.emplace_back("powershotsd900", 7.1100f);
    make_specs.emplace_back("powershotsd940is", 6.1600f);
    make_specs.emplace_back("powershotsd950is", 7.5300f);
    make_specs.emplace_back("powershotsd960is", 6.1600f);
    make_specs.emplace_back("powershotsd970is", 6.1600f);
    make_specs.emplace_back("powershotsd980is", 6.1600f);
    make_specs.emplace_back("powershotsd990is", 7.5300f);
    make_specs.emplace_back("powershotsx1is", 6.1600f);
    make_specs.emplace_back("powershotsx10is", 6.1600f);
    make_specs.emplace_back("powershotsx100is", 5.7500f);
    make_specs.emplace_back("powershotsx110is", 6.1600f);
    make_specs.emplace_back("powershotsx120is", 5.7500f);
    make_specs.emplace_back("powershotsx130is", 6.1600f);
    make_specs.emplace_back("powershotsx150is", 6.1600f);
    make_specs.emplace_back("powershotsx160is", 6.1600f);
    make_specs.emplace_back("powershotsx170is", 6.1600f);
    make_specs.emplace_back("powershotsx20is", 6.1600f);
    make_specs.emplace_back("powershotsx200is", 6.1600f);
    make_specs.emplace_back("powershotsx210is", 6.1600f);
    make_specs.emplace_back("powershotsx220hs", 6.1600f);
    make_specs.emplace_back("powershotsx230hs", 6.1600f);
    make_specs.emplace_back("powershotsx240hs", 6.1600f);
    make_specs.emplace_back("powershotsx260hs", 6.1600f);
    make_specs.emplace_back("powershotsx270hs", 6.1600f);
    make_specs.emplace_back("powershotsx280hs", 6.1600f);
    make_specs.emplace_back("powershotsx30is", 6.1600f);
    make_specs.emplace_back("powershotsx40hs", 6.1600f);
    make_specs.emplace_back("powershotsx400is", 6.1600f);
    make_specs.emplace_back("powershotsx410is", 6.1600f);
    make_specs.emplace_back("powershotsx50hs", 6.1600f);
    make_specs.emplace_back("powershotsx500is", 6.1600f);
    make_specs.emplace_back("powershotsx510hs", 6.1600f);
    make_specs.emplace_back("powershotsx520hs", 6.1600f);
    make_specs.emplace_back("powershotsx530hs", 6.1600f);
    make_specs.emplace_back("powershotsx60hs", 6.1600f);
    make_specs.emplace_back("powershotsx600hs", 6.1600f);
    make_specs.emplace_back("powershotsx610hs", 6.1600f);
    make_specs.emplace_back("powershotsx700hs", 6.1600f);
    make_specs.emplace_back("powershotsx710hs", 6.1600f);
    make_specs.emplace_back("powershottx1", 5.7500f);
    make_specs.emplace_back("pro90is", 7.1100f);
    make_specs.emplace_back("s200", 7.5300f);
    make_specs.emplace_back("sx220hs", 6.1600f);
  }

  {
    auto& make_specs = specs["casio"];
    make_specs.reserve(161);
    make_specs.emplace_back("exfr10", 6.1600f);
    make_specs.emplace_back("exilimexje10", 6.1600f);
    make_specs.emplace_back("exn10", 6.1600f);
    make_specs.emplace_back("exn1", 6.1600f);
    make_specs.emplace_back("exn20", 6.1600f);
    make_specs.emplace_back("exn50", 6.1600f);
    make_specs.emplace_back("exn5", 6.1600f);
    make_specs.emplace_back("extr10", 6.1600f);
    make_specs.emplace_back("extr15", 6.1600f);
    make_specs.emplace_back("exzr400", 6.1600f);
    make_specs.emplace_back("exzr700", 6.1600f);
    make_specs.emplace_back("exzr800", 6.1600f);
    make_specs.emplace_back("exzs30", 6.1600f);
    make_specs.emplace_back("exilimex100", 7.5300f);
    make_specs.emplace_back("exilimex10", 7.5300f);
    make_specs.emplace_back("exilimexfc100", 6.1600f);
    make_specs.emplace_back("exilimexfc150", 6.1600f);
    make_specs.emplace_back("exilimexfc160s", 6.1600f);
    make_specs.emplace_back("exilimexfh100", 6.1600f);
    make_specs.emplace_back("exilimexfh150", 6.1600f);
    make_specs.emplace_back("exilimexfh20", 6.1600f);
    make_specs.emplace_back("exilimexfh25", 6.1600f);
    make_specs.emplace_back("exilimexfs10", 6.1600f);
    make_specs.emplace_back("exilimexg1", 6.1600f);
    make_specs.emplace_back("exilimexh10", 6.1600f);
    make_specs.emplace_back("exilimexh15", 6.1600f);
    make_specs.emplace_back("exilimexh20g", 6.1600f);
    make_specs.emplace_back("exilimexh30", 6.1600f);
    make_specs.emplace_back("exilimexh50", 6.1600f);
    make_specs.emplace_back("exilimexh5", 6.1600f);
    make_specs.emplace_back("exilimexm1", 5.3300f);
    make_specs.emplace_back("exilimexm20", 5.3300f);
    make_specs.emplace_back("exilimexm2", 7.1100f);
    make_specs.emplace_back("exilimexp505", 5.7500f);
    make_specs.emplace_back("exilimexp600", 7.1100f);
    make_specs.emplace_back("exilimexp700", 7.1100f);
    make_specs.emplace_back("exilimexs100", 4.5000f);
    make_specs.emplace_back("exilimexs10", 6.1600f);
    make_specs.emplace_back("exilimexs12", 6.1600f);
    make_specs.emplace_back("exilimexs1", 5.3300f);
    make_specs.emplace_back("exilimexs200", 6.1600f);
    make_specs.emplace_back("exilimexs20", 5.3300f);
    make_specs.emplace_back("exilimexs2", 7.1100f);
    make_specs.emplace_back("exilimexs3", 7.1100f);
    make_specs.emplace_back("exilimexs500", 5.7500f);
    make_specs.emplace_back("exilimexs5", 6.1600f);
    make_specs.emplace_back("exilimexs600d", 5.7500f);
    make_specs.emplace_back("exilimexs600", 5.7500f);
    make_specs.emplace_back("exilimexs770d", 5.7500f);
    make_specs.emplace_back("exilimexs770", 5.7500f);
    make_specs.emplace_back("exilimexs7", 6.1600f);
    make_specs.emplace_back("exilimexs880", 5.7500f);
    make_specs.emplace_back("exilimexs8", 6.0800f);
    make_specs.emplace_back("exilimextr100", 6.1600f);
    make_specs.emplace_back("exilimextr150", 6.1600f);
    make_specs.emplace_back("exilimexv7", 5.7500f);
    make_specs.emplace_back("exilimexv8", 5.7500f);
    make_specs.emplace_back("exilimexz1000", 7.1100f);
    make_specs.emplace_back("exilimexz100", 6.1600f);
    make_specs.emplace_back("exilimexz1050", 7.3100f);
    make_specs.emplace_back("exilimexz1080", 7.3100f);
    make_specs.emplace_back("exilimexz10", 5.7500f);
    make_specs.emplace_back("exilimexz110", 5.7500f);
    make_specs.emplace_back("exilimexz1200sr", 7.5300f);
    make_specs.emplace_back("exilimexz120", 7.1100f);
    make_specs.emplace_back("exilimexz150", 5.7500f);
    make_specs.emplace_back("exilimexz16", 6.1600f);
    make_specs.emplace_back("exilimexz19", 5.7500f);
    make_specs.emplace_back("exilimexz1", 6.1600f);
    make_specs.emplace_back("exilimexz2000", 6.1600f);
    make_specs.emplace_back("exilimexz200", 6.1600f);
    make_specs.emplace_back("exilimexz20", 5.7500f);
    make_specs.emplace_back("exilimexz2300", 6.1600f);
    make_specs.emplace_back("exilimexz250", 5.7500f);
    make_specs.emplace_back("exilimexz25", 6.1600f);
    make_specs.emplace_back("exilimexz270", 5.7500f);
    make_specs.emplace_back("exilimexz280", 5.7500f);
    make_specs.emplace_back("exilimexz29", 5.7500f);
    make_specs.emplace_back("exilimexz2", 6.1600f);
    make_specs.emplace_back("exilimexz3000", 6.1600f);
    make_specs.emplace_back("exilimexz300", 6.1600f);
    make_specs.emplace_back("exilimexz30", 5.7500f);
    make_specs.emplace_back("exilimexz330", 6.1600f);
    make_specs.emplace_back("exilimexz33", 6.1600f);
    make_specs.emplace_back("exilimexz350", 6.1600f);
    make_specs.emplace_back("exilimexz35", 6.1600f);
    make_specs.emplace_back("exilimexz3", 5.7500f);
    make_specs.emplace_back("exilimexz400", 6.1600f);
    make_specs.emplace_back("exilimexz40", 5.7500f);
    make_specs.emplace_back("exilimexz450", 6.1600f);
    make_specs.emplace_back("exilimexz4", 5.7500f);
    make_specs.emplace_back("exilimexz500", 5.7500f);
    make_specs.emplace_back("exilimexz50", 5.7500f);
    make_specs.emplace_back("exilimexz550", 6.1600f);
    make_specs.emplace_back("exilimexz55", 5.7500f);
    make_specs.emplace_back("exilimexz57", 5.7500f);
    make_specs.emplace_back("exilimexz5", 5.7500f);
    make_specs.emplace_back("exilimexz600", 5.7500f);
    make_specs.emplace_back("exilimexz60", 7.1100f);
    make_specs.emplace_back("exilimexz65", 5.7500f);
    make_specs.emplace_back("exilimexz6", 5.7500f);
    make_specs.emplace_back("exilimexz700", 5.7500f);
    make_specs.emplace_back("exilimexz70", 5.7500f);
    make_specs.emplace_back("exilimexz750", 7.1100f);
    make_specs.emplace_back("exilimexz75", 5.7500f);
    make_specs.emplace_back("exilimexz77", 5.7500f);
    make_specs.emplace_back("exilimexz7", 5.7500f);
    make_specs.emplace_back("exilimexz800", 6.1600f);
    make_specs.emplace_back("exilimexz80", 5.7500f);
    make_specs.emplace_back("exilimexz850", 7.1100f);
    make_specs.emplace_back("exilimexz85", 5.7500f);
    make_specs.emplace_back("exilimexz8", 5.7500f);
    make_specs.emplace_back("exilimexz90", 6.1600f);
    make_specs.emplace_back("exilimexz9", 5.7500f);
    make_specs.emplace_back("exilimexzr1000", 6.1600f);
    make_specs.emplace_back("exilimexzr100", 6.1600f);
    make_specs.emplace_back("exilimexzr10", 6.1600f);
    make_specs.emplace_back("exilimexzr1100", 6.1600f);
    make_specs.emplace_back("exilimexzr15", 6.1600f);
    make_specs.emplace_back("exilimexzr200", 6.1600f);
    make_specs.emplace_back("exilimexzr20", 6.1600f);
    make_specs.emplace_back("exilimexzr300", 6.1600f);
    make_specs.emplace_back("exilimexzs100", 6.1600f);
    make_specs.emplace_back("exilimexzs10", 6.1600f);
    make_specs.emplace_back("exilimexzs12", 6.1600f);
    make_specs.emplace_back("exilimexzs150", 6.1600f);
    make_specs.emplace_back("exilimexzs15", 6.1600f);
    make_specs.emplace_back("exilimexzs20", 6.1600f);
    make_specs.emplace_back("exilimexzs5", 6.1600f);
    make_specs.emplace_back("exilimexzs6", 6.1600f);
    make_specs.emplace_back("exilimproexf1", 7.1100f);
    make_specs.emplace_back("exilimqvr100", 6.1600f);
    make_specs.emplace_back("exilimtryx", 6.1600f);
    make_specs.emplace_back("gv10", 4.5000f);
    make_specs.emplace_back("gv20", 4.5000f);
    make_specs.emplace_back("qv2000ux", 6.4000f);
    make_specs.emplace_back("qv2100", 5.3300f);
    make_specs.emplace_back("qv2300ux", 5.3300f);
    make_specs.emplace_back("qv2400ux", 5.3300f);
    make_specs.emplace_back("qv2800ux", 5.3300f);
    make_specs.emplace_back("qv2900ux", 5.3300f);
    make_specs.emplace_back("qv3000ex", 7.1100f);
    make_specs.emplace_back("qv300", 4.8000f);
    make_specs.emplace_back("qv3500ex", 7.1100f);
    make_specs.emplace_back("qv3ex/xv3", 7.1100f);
    make_specs.emplace_back("qv4000", 7.1100f);
    make_specs.emplace_back("qv5000sx", 4.8000f);
    make_specs.emplace_back("qv5500sx", 4.8000f);
    make_specs.emplace_back("qv5700", 7.1100f);
    make_specs.emplace_back("qv7000sx", 4.8000f);
    make_specs.emplace_back("qv700", 4.8000f);
    make_specs.emplace_back("qv770", 4.8000f);
    make_specs.emplace_back("qv8000sx", 4.8000f);
    make_specs.emplace_back("qvr3", 7.1100f);
    make_specs.emplace_back("qvr40", 7.1100f);
    make_specs.emplace_back("qvr41", 7.1100f);
    make_specs.emplace_back("qvr4", 7.1100f);
    make_specs.emplace_back("qvr51", 7.1100f);
    make_specs.emplace_back("qvr52", 7.1100f);
    make_specs.emplace_back("qvr61", 7.1100f);
    make_specs.emplace_back("qvr62", 7.1100f);
  }

  {
    auto& make_specs = specs["concord"];
    make_specs.reserve(40);
    make_specs.emplace_back("1500", 6.4000f);
    make_specs.emplace_back("3043", 6.4000f);
    make_specs.emplace_back("3045", 6.4000f);
    make_specs.emplace_back("3046", 6.4000f);
    make_specs.emplace_back("3047", 6.4000f);
    make_specs.emplace_back("3345z", 6.4000f);
    make_specs.emplace_back("3346z", 6.4000f);
    make_specs.emplace_back("4042", 5.7500f);
    make_specs.emplace_back("4340z", 5.7500f);
    make_specs.emplace_back("5040", 7.1100f);
    make_specs.emplace_back("5340z", 5.7500f);
    make_specs.emplace_back("5345z", 7.1100f);
    make_specs.emplace_back("6340z", 7.1100f);
    make_specs.emplace_back("642", 6.4000f);
    make_specs.emplace_back("dvx", 6.4000f);
    make_specs.emplace_back("es500z", 5.7500f);
    make_specs.emplace_back("es510z", 7.1100f);
    make_specs.emplace_back("eyeq1000", 6.4000f);
    make_specs.emplace_back("eyeq1300", 6.4000f);
    make_specs.emplace_back("eyeq2040", 6.4000f);
    make_specs.emplace_back("eyeq2133z", 6.4000f);
    make_specs.emplace_back("eyeq3040af", 6.4000f);
    make_specs.emplace_back("eyeq3103", 6.4000f);
    make_specs.emplace_back("eyeq3120af", 4.0000f);
    make_specs.emplace_back("eyeq3132z", 6.4000f);
    make_specs.emplace_back("eyeq3340z", 5.3300f);
    make_specs.emplace_back("eyeq3341z", 7.1100f);
    make_specs.emplace_back("eyeq3343z", 5.3300f);
    make_specs.emplace_back("eyeq4060af", 7.1100f);
    make_specs.emplace_back("eyeq4330z", 7.1100f);
    make_specs.emplace_back("eyeq4342z", 7.1100f);
    make_specs.emplace_back("eyeq4360z", 7.1100f);
    make_specs.emplace_back("eyeq4363z", 7.1100f);
    make_specs.emplace_back("eyeq5062af", 7.1100f);
    make_specs.emplace_back("eyeq5330z", 7.1100f);
    make_specs.emplace_back("eyeqduo2000", 7.1100f);
    make_specs.emplace_back("eyeqduolcd", 6.4000f);
    make_specs.emplace_back("eyeqgo2000", 7.1100f);
    make_specs.emplace_back("eyeqgolcd", 6.4000f);
    make_specs.emplace_back("eyeqgowireless", 7.1100f);
  }

  {
    auto& make_specs = specs["contax"];
    make_specs.reserve(5);
    make_specs.emplace_back("i4r", 5.3300f);
    make_specs.emplace_back("ndigital", 36.0000f);
    make_specs.emplace_back("sl300rt", 5.3300f);
    make_specs.emplace_back("tvsdigital", 7.1100f);
    make_specs.emplace_back("u4r", 5.3300f);
  }

  {
    auto& make_specs = specs["epson"];
    make_specs.reserve(19);
    make_specs.emplace_back("l500v", 5.7500f);
    make_specs.emplace_back("photopc3000zoom", 7.1100f);
    make_specs.emplace_back("photopc3100zoom", 7.1100f);
    make_specs.emplace_back("photopc500", 4.8000f);
    make_specs.emplace_back("photopc550", 4.8000f);
    make_specs.emplace_back("photopc600", 4.8000f);
    make_specs.emplace_back("photopc650", 4.8000f);
    make_specs.emplace_back("photopc700", 4.8000f);
    make_specs.emplace_back("photopc750zoom", 6.4000f);
    make_specs.emplace_back("photopc800", 6.4000f);
    make_specs.emplace_back("photopc850zoom", 6.4000f);
    make_specs.emplace_back("photopcl200", 5.7500f);
    make_specs.emplace_back("photopcl300", 5.7500f);
    make_specs.emplace_back("photopcl400", 5.7500f);
    make_specs.emplace_back("photopcl410", 5.7500f);
    make_specs.emplace_back("photopcl500v", 5.7500f);
    make_specs.emplace_back("rd1xg", 23.7000f);
    make_specs.emplace_back("rd1", 23.7000f);
    make_specs.emplace_back("rd1s", 23.7000f);
  }

  {
    auto& make_specs = specs["fujifilm"];
    make_specs.reserve(365);
    make_specs.emplace_back("a850", 5.7500f);
    make_specs.emplace_back("bigjobhd3w", 5.7500f);
    make_specs.emplace_back("bigjobhd1", 5.3300f);
    make_specs.emplace_back("digitalq1", 6.4000f);
    make_specs.emplace_back("ds260hd", 6.4000f);
    make_specs.emplace_back("ds300", 8.8000f);
    make_specs.emplace_back("finepix1300", 5.3300f);
    make_specs.emplace_back("finepix1400z", 5.3300f);
    make_specs.emplace_back("finepix2300", 5.3300f);
    make_specs.emplace_back("finepix2400zoom", 5.3300f);
    make_specs.emplace_back("finepix2600zoom", 5.3300f);
    make_specs.emplace_back("finepix2650", 5.3300f);
    make_specs.emplace_back("finepix2800zoom", 5.3300f);
    make_specs.emplace_back("finepix30i", 5.3300f);
    make_specs.emplace_back("finepix3800", 5.3300f);
    make_specs.emplace_back("finepix40i", 5.3300f);
    make_specs.emplace_back("finepix4700zoom", 7.5300f);
    make_specs.emplace_back("finepix4800zoom", 7.5300f);
    make_specs.emplace_back("finepix4900zoom", 7.5300f);
    make_specs.emplace_back("finepix50i", 7.5300f);
    make_specs.emplace_back("finepix6800zoom", 7.5300f);
    make_specs.emplace_back("finepix6900zoom", 7.5300f);
    make_specs.emplace_back("finepixa100", 6.1600f);
    make_specs.emplace_back("finepixa101", 5.3300f);
    make_specs.emplace_back("finepixa120", 5.3300f);
    make_specs.emplace_back("finepixa150", 6.1600f);
    make_specs.emplace_back("finepixa160", 6.1600f);
    make_specs.emplace_back("finepixa170", 6.1600f);
    make_specs.emplace_back("finepixa175", 6.1600f);
    make_specs.emplace_back("finepixa180", 6.1600f);
    make_specs.emplace_back("finepixa200", 5.3300f);
    make_specs.emplace_back("finepixa201", 5.3300f);
    make_specs.emplace_back("finepixa202", 5.3300f);
    make_specs.emplace_back("finepixa203", 5.3300f);
    make_specs.emplace_back("finepixa204", 5.3300f);
    make_specs.emplace_back("finepixa205zoom", 5.3300f);
    make_specs.emplace_back("finepixa210zoom", 5.3300f);
    make_specs.emplace_back("finepixa220", 6.1600f);
    make_specs.emplace_back("finepixa225", 6.1600f);
    make_specs.emplace_back("finepixa235", 6.1600f);
    make_specs.emplace_back("finepixa303", 5.3300f);
    make_specs.emplace_back("finepixa310zoom", 5.3300f);
    make_specs.emplace_back("finepixa330", 5.3300f);
    make_specs.emplace_back("finepixa340", 5.3300f);
    make_specs.emplace_back("finepixa345zoom", 5.7500f);
    make_specs.emplace_back("finepixa350zoom", 5.7500f);
    make_specs.emplace_back("finepixa400zoom", 5.7500f);
    make_specs.emplace_back("finepixa500zoom", 5.7500f);
    make_specs.emplace_back("finepixa510", 5.7500f);
    make_specs.emplace_back("finepixa600zoom", 7.5300f);
    make_specs.emplace_back("finepixa610", 5.7500f);
    make_specs.emplace_back("finepixa700", 8.0000f);
    make_specs.emplace_back("finepixa800", 8.0000f);
    make_specs.emplace_back("finepixa820", 8.0000f);
    make_specs.emplace_back("finepixa825", 8.0000f);
    make_specs.emplace_back("finepixa900", 8.0000f);
    make_specs.emplace_back("finepixa920", 8.0000f);
    make_specs.emplace_back("finepixav100", 6.1600f);
    make_specs.emplace_back("finepixav105", 6.1600f);
    make_specs.emplace_back("finepixav110", 6.1600f);
    make_specs.emplace_back("finepixav130", 6.1600f);
    make_specs.emplace_back("finepixav140", 6.1600f);
    make_specs.emplace_back("finepixav150", 6.1600f);
    make_specs.emplace_back("finepixav180", 6.1600f);
    make_specs.emplace_back("finepixav200", 6.1600f);
    make_specs.emplace_back("finepixav205", 6.1600f);
    make_specs.emplace_back("finepixav250", 6.1600f);
    make_specs.emplace_back("finepixav255", 6.1600f);
    make_specs.emplace_back("finepixax200", 6.1600f);
    make_specs.emplace_back("finepixax205", 6.1600f);
    make_specs.emplace_back("finepixax230", 6.1600f);
    make_specs.emplace_back("finepixax245w", 6.1600f);
    make_specs.emplace_back("finepixax250", 6.1600f);
    make_specs.emplace_back("finepixax280", 6.1600f);
    make_specs.emplace_back("finepixax300", 6.1600f);
    make_specs.emplace_back("finepixax305", 6.1600f);
    make_specs.emplace_back("finepixax350", 6.1600f);
    make_specs.emplace_back("finepixax355", 6.1600f);
    make_specs.emplace_back("finepixax500", 6.1600f);
    make_specs.emplace_back("finepixax550", 6.1600f);
    make_specs.emplace_back("finepixax650", 6.1600f);
    make_specs.emplace_back("finepixe500zoom", 5.7500f);
    make_specs.emplace_back("finepixe510zoom", 5.7500f);
    make_specs.emplace_back("finepixe550zoom", 7.5300f);
    make_specs.emplace_back("finepixe900zoom", 8.0000f);
    make_specs.emplace_back("finepixex20", 5.3300f);
    make_specs.emplace_back("finepixf10zoom", 7.5300f);
    make_specs.emplace_back("finepixf100fd", 8.0000f);
    make_specs.emplace_back("finepixf11zoom", 7.5300f);
    make_specs.emplace_back("finepixf20zoom", 7.5300f);
    make_specs.emplace_back("finepixf200exr", 8.0000f);
    make_specs.emplace_back("finepixf30zoom", 7.5300f);
    make_specs.emplace_back("finepixf300exr", 6.4000f);
    make_specs.emplace_back("finepixf305exr", 6.4000f);
    make_specs.emplace_back("finepixf31fd", 7.5300f);
    make_specs.emplace_back("finepixf401zoom", 5.3300f);
    make_specs.emplace_back("finepixf402", 5.3300f);
    make_specs.emplace_back("finepixf40fd", 8.0000f);
    make_specs.emplace_back("finepixf410zoom", 5.3300f);
    make_specs.emplace_back("finepixf420zoom", 5.3300f);
    make_specs.emplace_back("finepixf440zoom", 5.7500f);
    make_specs.emplace_back("finepixf450zoom", 5.7500f);
    make_specs.emplace_back("finepixf455zoom", 5.7500f);
    make_specs.emplace_back("finepixf45fd", 8.0000f);
    make_specs.emplace_back("finepixf460", 5.7500f);
    make_specs.emplace_back("finepixf470zoom", 5.7500f);
    make_specs.emplace_back("finepixf47fd", 8.0000f);
    make_specs.emplace_back("finepixf480zoom", 5.7500f);
    make_specs.emplace_back("finepixf500exr", 6.4000f);
    make_specs.emplace_back("finepixf50fd", 8.0000f);
    make_specs.emplace_back("finepixf550exr", 6.4000f);
    make_specs.emplace_back("finepixf600exr", 6.4000f);
    make_specs.emplace_back("finepixf601zoom", 7.5300f);
    make_specs.emplace_back("finepixf605exr", 6.4000f);
    make_specs.emplace_back("finepixf60fd", 8.0000f);
    make_specs.emplace_back("finepixf610", 7.5300f);
    make_specs.emplace_back("finepixf650zoom", 5.7500f);
    make_specs.emplace_back("finepixf660exr", 6.4000f);
    make_specs.emplace_back("finepixf700", 7.5300f);
    make_specs.emplace_back("finepixf70exr", 6.4000f);
    make_specs.emplace_back("finepixf710", 7.5300f);
    make_specs.emplace_back("finepixf72exr", 6.4000f);
    make_specs.emplace_back("finepixf750exr", 6.4000f);
    make_specs.emplace_back("finepixf75exr", 6.4000f);
    make_specs.emplace_back("finepixf770exr", 6.4000f);
    make_specs.emplace_back("finepixf800exr", 6.4000f);
    make_specs.emplace_back("finepixf80exr", 6.4000f);
    make_specs.emplace_back("finepixf810zoom", 7.5300f);
    make_specs.emplace_back("finepixf850exr", 6.4000f);
    make_specs.emplace_back("finepixf85exr", 6.4000f);
    make_specs.emplace_back("finepixf900exr", 6.4000f);
    make_specs.emplace_back("finepixhs10", 6.1600f);
    make_specs.emplace_back("finepixhs11", 6.1600f);
    make_specs.emplace_back("finepixhs20exr", 6.4000f);
    make_specs.emplace_back("finepixhs22exr", 6.4000f);
    make_specs.emplace_back("finepixhs25exr", 6.4000f);
    make_specs.emplace_back("finepixhs30exr", 6.4000f);
    make_specs.emplace_back("finepixhs35exr", 6.4000f);
    make_specs.emplace_back("finepixhs50exr", 6.4000f);
    make_specs.emplace_back("finepixis1", 8.0000f);
    make_specs.emplace_back("finepixispro", 23.0000f);
    make_specs.emplace_back("finepixj100", 6.1600f);
    make_specs.emplace_back("finepixj10", 5.7500f);
    make_specs.emplace_back("finepixj110w", 6.1600f);
    make_specs.emplace_back("finepixj120", 6.1600f);
    make_specs.emplace_back("finepixj12", 5.7500f);
    make_specs.emplace_back("finepixj150w", 6.1600f);
    make_specs.emplace_back("finepixj15", 5.7500f);
    make_specs.emplace_back("finepixj20", 6.1600f);
    make_specs.emplace_back("finepixj210", 6.1600f);
    make_specs.emplace_back("finepixj22", 6.1600f);
    make_specs.emplace_back("finepixj250", 6.1600f);
    make_specs.emplace_back("finepixj25", 6.1600f);
    make_specs.emplace_back("finepixj26", 6.1600f);
    make_specs.emplace_back("finepixj27", 6.1600f);
    make_specs.emplace_back("finepixj28", 6.1600f);
    make_specs.emplace_back("finepixj29", 6.1600f);
    make_specs.emplace_back("finepixj30", 6.1600f);
    make_specs.emplace_back("finepixj32", 6.1600f);
    make_specs.emplace_back("finepixj35", 6.1600f);
    make_specs.emplace_back("finepixj37", 6.1600f);
    make_specs.emplace_back("finepixj38", 6.1600f);
    make_specs.emplace_back("finepixj50", 5.7500f);
    make_specs.emplace_back("finepixjv100", 6.1600f);
    make_specs.emplace_back("finepixjv105", 6.1600f);
    make_specs.emplace_back("finepixjv110", 6.1600f);
    make_specs.emplace_back("finepixjv150", 6.1600f);
    make_specs.emplace_back("finepixjv200", 6.1600f);
    make_specs.emplace_back("finepixjv205", 6.1600f);
    make_specs.emplace_back("finepixjv250", 6.1600f);
    make_specs.emplace_back("finepixjv255", 6.1600f);
    make_specs.emplace_back("finepixjx200", 6.1600f);
    make_specs.emplace_back("finepixjx205", 6.1600f);
    make_specs.emplace_back("finepixjx210", 6.1600f);
    make_specs.emplace_back("finepixjx250", 6.1600f);
    make_specs.emplace_back("finepixjx280", 6.1600f);
    make_specs.emplace_back("finepixjx300", 6.1600f);
    make_specs.emplace_back("finepixjx305", 6.1600f);
    make_specs.emplace_back("finepixjx350", 6.1600f);
    make_specs.emplace_back("finepixjx355", 6.1600f);
    make_specs.emplace_back("finepixjx370", 6.1600f);
    make_specs.emplace_back("finepixjx375", 6.1600f);
    make_specs.emplace_back("finepixjx400", 6.1600f);
    make_specs.emplace_back("finepixjx405", 6.1600f);
    make_specs.emplace_back("finepixjx420", 6.1600f);
    make_specs.emplace_back("finepixjx500", 6.1600f);
    make_specs.emplace_back("finepixjx520", 6.1600f);
    make_specs.emplace_back("finepixjx530", 6.1600f);
    make_specs.emplace_back("finepixjx550", 6.1600f);
    make_specs.emplace_back("finepixjx580", 6.1600f);
    make_specs.emplace_back("finepixjx700", 6.1600f);
    make_specs.emplace_back("finepixjz100", 6.1600f);
    make_specs.emplace_back("finepixjz200", 6.1600f);
    make_specs.emplace_back("finepixjz250", 6.1600f);
    make_specs.emplace_back("finepixjz300", 6.1600f);
    make_specs.emplace_back("finepixjz305", 6.1600f);
    make_specs.emplace_back("finepixjz310", 6.1600f);
    make_specs.emplace_back("finepixjz500", 6.1600f);
    make_specs.emplace_back("finepixjz505", 6.1600f);
    make_specs.emplace_back("finepixjz510", 6.1600f);
    make_specs.emplace_back("finepixjz700", 6.1600f);
    make_specs.emplace_back("finepixm603", 7.5300f);
    make_specs.emplace_back("finepixpr21", 6.4000f);
    make_specs.emplace_back("finepixreal3dw1", 6.1600f);
    make_specs.emplace_back("finepixreal3dw3", 6.1600f);
    make_specs.emplace_back("finepixs1pro", 23.0000f);
    make_specs.emplace_back("finepixs1000fd", 6.1600f);
    make_specs.emplace_back("finepixs100fs", 8.8000f);
    make_specs.emplace_back("finepixs1500", 6.1600f);
    make_specs.emplace_back("finepixs1600", 6.1600f);
    make_specs.emplace_back("finepixs1700", 6.1600f);
    make_specs.emplace_back("finepixs1730", 6.1600f);
    make_specs.emplace_back("finepixs1770", 6.1600f);
    make_specs.emplace_back("finepixs1800", 6.1600f);
    make_specs.emplace_back("finepixs1850", 6.1600f);
    make_specs.emplace_back("finepixs1880", 6.1600f);
    make_specs.emplace_back("finepixs1900", 6.1600f);
    make_specs.emplace_back("finepixs1", 6.1600f);
    make_specs.emplace_back("finepixs2pro", 23.0000f);
    make_specs.emplace_back("finepixs20pro", 7.5300f);
    make_specs.emplace_back("finepixs2000hd", 6.1600f);
    make_specs.emplace_back("finepixs200exr", 8.0000f);
    make_specs.emplace_back("finepixs205exr", 8.0000f);
    make_specs.emplace_back("finepixs2500hd", 6.1600f);
    make_specs.emplace_back("finepixs2550hd", 6.1600f);
    make_specs.emplace_back("finepixs2600hd", 6.1600f);
    make_specs.emplace_back("finepixs2800hd", 6.1600f);
    make_specs.emplace_back("finepixs2900hd", 6.1600f);
    make_specs.emplace_back("finepixs2950", 6.1600f);
    make_specs.emplace_back("finepixs2980", 6.1600f);
    make_specs.emplace_back("finepixs2990", 6.1600f);
    make_specs.emplace_back("finepixs3pro", 23.5000f);
    make_specs.emplace_back("finepixs3000z", 5.3300f);
    make_specs.emplace_back("finepixs304", 5.3300f);
    make_specs.emplace_back("finepixs3200", 6.1600f);
    make_specs.emplace_back("finepixs3250", 6.1600f);
    make_specs.emplace_back("finepixs3300", 6.1600f);
    make_specs.emplace_back("finepixs3350", 6.1600f);
    make_specs.emplace_back("finepixs3400", 6.1600f);
    make_specs.emplace_back("finepixs3450", 6.1600f);
    make_specs.emplace_back("finepixs3500zoom", 5.3300f);
    make_specs.emplace_back("finepixs4000", 6.1600f);
    make_specs.emplace_back("finepixs4050", 6.1600f);
    make_specs.emplace_back("finepixs4200", 6.1600f);
    make_specs.emplace_back("finepixs4300", 6.1600f);
    make_specs.emplace_back("finepixs4400", 6.1600f);
    make_specs.emplace_back("finepixs4500", 6.1600f);
    make_specs.emplace_back("finepixs4600", 6.1600f);
    make_specs.emplace_back("finepixs4700", 6.1600f);
    make_specs.emplace_back("finepixs4800", 6.1600f);
    make_specs.emplace_back("finepixs5pro", 23.0000f);
    make_specs.emplace_back("finepixs5000zoom", 5.3300f);
    make_specs.emplace_back("finepixs5100zoom", 5.3300f);
    make_specs.emplace_back("finepixs5200zoom", 5.7500f);
    make_specs.emplace_back("finepixs5500zoom", 5.3300f);
    make_specs.emplace_back("finepixs5600zoom", 5.7500f);
    make_specs.emplace_back("finepixs5700zoom", 5.7500f);
    make_specs.emplace_back("finepixs5800", 5.7500f);
    make_specs.emplace_back("finepixs6000fd", 7.5300f);
    make_specs.emplace_back("finepixs602zoom", 7.5300f);
    make_specs.emplace_back("finepixs602zpro", 7.5300f);
    make_specs.emplace_back("finepixs6500fd", 7.5300f);
    make_specs.emplace_back("finepixs6600", 6.1600f);
    make_specs.emplace_back("finepixs6700", 6.1600f);
    make_specs.emplace_back("finepixs6800", 6.1600f);
    make_specs.emplace_back("finepixs7000zoom", 7.5300f);
    make_specs.emplace_back("finepixs8000fd", 6.0300f);
    make_specs.emplace_back("finepixs8100fd", 6.1600f);
    make_specs.emplace_back("finepixs8200", 6.1600f);
    make_specs.emplace_back("finepixs8300", 6.1600f);
    make_specs.emplace_back("finepixs8400", 6.1600f);
    make_specs.emplace_back("finepixs8500", 6.1600f);
    make_specs.emplace_back("finepixs8600", 6.1600f);
    make_specs.emplace_back("finepixs9000zoom", 8.0000f);
    make_specs.emplace_back("finepixs9100", 8.0000f);
    make_specs.emplace_back("finepixs9200", 6.1600f);
    make_specs.emplace_back("finepixs9400w", 6.1600f);
    make_specs.emplace_back("finepixs9500", 8.0000f);
    make_specs.emplace_back("finepixs9600", 8.0000f);
    make_specs.emplace_back("finepixs9800", 6.1600f);
    make_specs.emplace_back("finepixs9900w", 6.1600f);
    make_specs.emplace_back("finepixsl1000", 6.1600f);
    make_specs.emplace_back("finepixsl240", 6.1600f);
    make_specs.emplace_back("finepixsl260", 6.1600f);
    make_specs.emplace_back("finepixsl280", 6.1600f);
    make_specs.emplace_back("finepixsl300", 6.1600f);
    make_specs.emplace_back("finepixt200", 6.1600f);
    make_specs.emplace_back("finepixt205", 6.1600f);
    make_specs.emplace_back("finepixt300", 6.1600f);
    make_specs.emplace_back("finepixt305", 6.1600f);
    make_specs.emplace_back("finepixt350", 6.1600f);
    make_specs.emplace_back("finepixt400", 6.1600f);
    make_specs.emplace_back("finepixt500", 6.1600f);
    make_specs.emplace_back("finepixt550", 6.1600f);
    make_specs.emplace_back("finepixv10zoom", 5.7500f);
    make_specs.emplace_back("finepixx100", 23.6000f);
    make_specs.emplace_back("finepixxp100", 6.1600f);
    make_specs.emplace_back("finepixxp10", 6.1600f);
    make_specs.emplace_back("finepixxp11", 6.1600f);
    make_specs.emplace_back("finepixxp150", 6.1600f);
    make_specs.emplace_back("finepixxp170", 6.1600f);
    make_specs.emplace_back("finepixxp200", 6.1600f);
    make_specs.emplace_back("finepixxp20", 6.1600f);
    make_specs.emplace_back("finepixxp22", 6.1600f);
    make_specs.emplace_back("finepixxp30", 6.1600f);
    make_specs.emplace_back("finepixxp33", 6.1600f);
    make_specs.emplace_back("finepixxp50", 6.1600f);
    make_specs.emplace_back("finepixxp60", 6.1600f);
    make_specs.emplace_back("finepixxp70", 6.1600f);
    make_specs.emplace_back("finepixxp80", 6.1600f);
    make_specs.emplace_back("finepixz1000exr", 6.4000f);
    make_specs.emplace_back("finepixz100fd", 5.7500f);
    make_specs.emplace_back("finepixz10fd", 5.7500f);
    make_specs.emplace_back("finepixz110", 6.1600f);
    make_specs.emplace_back("finepixz1", 5.7500f);
    make_specs.emplace_back("finepixz200fd", 6.1600f);
    make_specs.emplace_back("finepixz20fd", 6.1600f);
    make_specs.emplace_back("finepixz2", 5.7500f);
    make_specs.emplace_back("finepixz300", 6.1600f);
    make_specs.emplace_back("finepixz30", 6.1600f);
    make_specs.emplace_back("finepixz31", 6.1600f);
    make_specs.emplace_back("finepixz33wp", 6.1600f);
    make_specs.emplace_back("finepixz35", 6.1600f);
    make_specs.emplace_back("finepixz37", 6.1600f);
    make_specs.emplace_back("finepixz3", 5.7500f);
    make_specs.emplace_back("finepixz5fd", 5.7500f);
    make_specs.emplace_back("finepixz700exr", 6.4000f);
    make_specs.emplace_back("finepixz707exr", 6.4000f);
    make_specs.emplace_back("finepixz70", 6.1600f);
    make_specs.emplace_back("finepixz71", 6.1600f);
    make_specs.emplace_back("finepixz800exr", 6.4000f);
    make_specs.emplace_back("finepixz808exr", 6.4000f);
    make_specs.emplace_back("finepixz80", 6.1600f);
    make_specs.emplace_back("finepixz81", 6.1600f);
    make_specs.emplace_back("finepixz900exr", 6.4000f);
    make_specs.emplace_back("finepixz909exr", 6.4000f);
    make_specs.emplace_back("finepixz90", 6.1600f);
    make_specs.emplace_back("finepixz91", 6.1600f);
    make_specs.emplace_back("finepixz950exr", 6.4000f);
    make_specs.emplace_back("mx1200", 6.4000f);
    make_specs.emplace_back("mx1400", 5.3300f);
    make_specs.emplace_back("mx1500", 6.4000f);
    make_specs.emplace_back("mx1700", 6.4000f);
    make_specs.emplace_back("mx2700", 6.4000f);
    make_specs.emplace_back("mx2900zoom", 6.4000f);
    make_specs.emplace_back("mx500", 6.4000f);
    make_specs.emplace_back("mx600zoom", 6.4000f);
    make_specs.emplace_back("mx700", 6.4000f);
    make_specs.emplace_back("xa1", 23.6000f);
    make_specs.emplace_back("xa2", 23.6000f);
    make_specs.emplace_back("xe1", 23.6000f);
    make_specs.emplace_back("xe2", 23.6000f);
    make_specs.emplace_back("xm1", 23.6000f);
    make_specs.emplace_back("xpro1", 23.6000f);
    make_specs.emplace_back("xs1", 8.8000f);
    make_specs.emplace_back("xt10", 23.6000f);
    make_specs.emplace_back("xt1", 23.6000f);
    make_specs.emplace_back("x100s", 23.6000f);
    make_specs.emplace_back("x100t", 23.6000f);
    make_specs.emplace_back("x10", 8.8000f);
    make_specs.emplace_back("x20", 8.8000f);
    make_specs.emplace_back("x30", 8.8000f);
    make_specs.emplace_back("xf1", 8.8000f);
    make_specs.emplace_back("xq1", 8.8000f);
    make_specs.emplace_back("xq2", 8.8000f);
  }

  {
    auto& make_specs = specs["ge"];
    make_specs.reserve(54);
    make_specs.emplace_back("a1030", 7.5300f);
    make_specs.emplace_back("a1035", 6.0800f);
    make_specs.emplace_back("a1050", 6.0800f);
    make_specs.emplace_back("a1235", 6.1600f);
    make_specs.emplace_back("a1250", 6.1600f);
    make_specs.emplace_back("a1255", 6.0800f);
    make_specs.emplace_back("a1455", 6.1600f);
    make_specs.emplace_back("a1456w", 6.1600f);
    make_specs.emplace_back("a730", 5.7500f);
    make_specs.emplace_back("a735", 5.7500f);
    make_specs.emplace_back("a830", 5.7500f);
    make_specs.emplace_back("a835", 5.7500f);
    make_specs.emplace_back("a950", 6.0800f);
    make_specs.emplace_back("c1033", 6.0800f);
    make_specs.emplace_back("c1233", 6.0800f);
    make_specs.emplace_back("c1433", 6.1600f);
    make_specs.emplace_back("c1440w", 6.1600f);
    make_specs.emplace_back("create", 6.1600f);
    make_specs.emplace_back("e1030", 7.5300f);
    make_specs.emplace_back("e1035", 7.5300f);
    make_specs.emplace_back("e1040", 7.5300f);
    make_specs.emplace_back("e1050tw", 6.0800f);
    make_specs.emplace_back("e1050", 6.0800f);
    make_specs.emplace_back("e1055w", 6.0800f);
    make_specs.emplace_back("e1235", 7.4400f);
    make_specs.emplace_back("e1240", 7.5300f);
    make_specs.emplace_back("e1250tw", 6.1600f);
    make_specs.emplace_back("e1255w", 6.1600f);
    make_specs.emplace_back("e1276w", 6.1600f);
    make_specs.emplace_back("e1410sw", 6.1600f);
    make_specs.emplace_back("e1450w", 6.1600f);
    make_specs.emplace_back("e1480w", 6.1600f);
    make_specs.emplace_back("e1486tw", 6.1600f);
    make_specs.emplace_back("e1680w", 6.0800f);
    make_specs.emplace_back("e840s", 5.7500f);
    make_specs.emplace_back("e850", 5.7500f);
    make_specs.emplace_back("g100", 6.1600f);
    make_specs.emplace_back("g2", 6.0300f);
    make_specs.emplace_back("g3wp", 6.1600f);
    make_specs.emplace_back("g3", 6.0800f);
    make_specs.emplace_back("g5wp", 6.1600f);
    make_specs.emplace_back("g1", 5.7500f);
    make_specs.emplace_back("j1050", 6.0800f);
    make_specs.emplace_back("j1250", 6.1600f);
    make_specs.emplace_back("j1455", 6.1600f);
    make_specs.emplace_back("j1456w", 6.1600f);
    make_specs.emplace_back("j1458w", 6.1600f);
    make_specs.emplace_back("j1470s", 6.0800f);
    make_specs.emplace_back("pj1", 6.1600f);
    make_specs.emplace_back("x1", 5.7500f);
    make_specs.emplace_back("x3", 6.0800f);
    make_specs.emplace_back("x500", 6.1600f);
    make_specs.emplace_back("x550", 6.1600f);
    make_specs.emplace_back("x600", 6.1600f);
  }

  {
    auto& make_specs = specs["hp"];
    make_specs.reserve(69);
    make_specs.emplace_back("ca350", 6.1600f);
    make_specs.emplace_back("cb350", 6.1600f);
    make_specs.emplace_back("cw450t", 6.1600f);
    make_specs.emplace_back("cw450", 6.1600f);
    make_specs.emplace_back("photosmart120", 6.4000f);
    make_specs.emplace_back("photosmart318", 5.3300f);
    make_specs.emplace_back("photosmart320", 5.3300f);
    make_specs.emplace_back("photosmart435", 5.3300f);
    make_specs.emplace_back("photosmart612", 5.3300f);
    make_specs.emplace_back("photosmart620", 4.8000f);
    make_specs.emplace_back("photosmart635", 4.5000f);
    make_specs.emplace_back("photosmart715", 7.1100f);
    make_specs.emplace_back("photosmart720", 7.1100f);
    make_specs.emplace_back("photosmart733", 5.3300f);
    make_specs.emplace_back("photosmart735", 5.3300f);
    make_specs.emplace_back("photosmart812", 7.1100f);
    make_specs.emplace_back("photosmart850", 7.1100f);
    make_specs.emplace_back("photosmart935", 7.1100f);
    make_specs.emplace_back("photosmart945", 7.1100f);
    make_specs.emplace_back("photosmartc200", 8.8000f);
    make_specs.emplace_back("photosmartc20", 8.8000f);
    make_specs.emplace_back("photosmartc215", 5.3300f);
    make_specs.emplace_back("photosmartc30", 8.8000f);
    make_specs.emplace_back("photosmartc315", 5.3300f);
    make_specs.emplace_back("photosmartc500", 8.8000f);
    make_specs.emplace_back("photosmartc618", 5.3300f);
    make_specs.emplace_back("photosmartc912", 8.8000f);
    make_specs.emplace_back("photosmarte317", 5.7500f);
    make_specs.emplace_back("photosmarte327", 5.7500f);
    make_specs.emplace_back("photosmarte337", 5.7500f);
    make_specs.emplace_back("photosmarte427", 5.7500f);
    make_specs.emplace_back("photosmartm22", 5.7500f);
    make_specs.emplace_back("photosmartm23", 5.7500f);
    make_specs.emplace_back("photosmartm307", 5.3300f);
    make_specs.emplace_back("photosmartm407", 5.7500f);
    make_specs.emplace_back("photosmartm417", 5.7500f);
    make_specs.emplace_back("photosmartm425", 5.7500f);
    make_specs.emplace_back("photosmartm437", 5.7500f);
    make_specs.emplace_back("photosmartm447", 5.7500f);
    make_specs.emplace_back("photosmartm517", 5.7500f);
    make_specs.emplace_back("photosmartm525", 5.7500f);
    make_specs.emplace_back("photosmartm527", 5.7500f);
    make_specs.emplace_back("photosmartm537", 5.7500f);
    make_specs.emplace_back("photosmartm547", 5.7500f);
    make_specs.emplace_back("photosmartm627", 5.7500f);
    make_specs.emplace_back("photosmartm637", 5.7500f);
    make_specs.emplace_back("photosmartm737", 5.7500f);
    make_specs.emplace_back("photosmartmz67", 7.1100f);
    make_specs.emplace_back("photosmartr507", 5.7500f);
    make_specs.emplace_back("photosmartr607", 5.7500f);
    make_specs.emplace_back("photosmartr707", 7.1100f);
    make_specs.emplace_back("photosmartr717", 7.1100f);
    make_specs.emplace_back("photosmartr725", 5.7500f);
    make_specs.emplace_back("photosmartr727", 5.7500f);
    make_specs.emplace_back("photosmartr742", 5.7500f);
    make_specs.emplace_back("photosmartr817", 5.7500f);
    make_specs.emplace_back("photosmartr818", 5.7500f);
    make_specs.emplace_back("photosmartr827", 5.7500f);
    make_specs.emplace_back("photosmartr837", 5.7500f);
    make_specs.emplace_back("photosmartr847", 5.7500f);
    make_specs.emplace_back("photosmartr927", 7.1100f);
    make_specs.emplace_back("photosmartr937", 5.7500f);
    make_specs.emplace_back("photosmartr967", 7.1100f);
    make_specs.emplace_back("pw460t", 6.1600f);
    make_specs.emplace_back("pw550", 6.1600f);
    make_specs.emplace_back("r607bmw", 5.7500f);
    make_specs.emplace_back("r607harajuku", 5.7500f);
    make_specs.emplace_back("sb360", 6.1600f);
    make_specs.emplace_back("sw450", 6.1600f);
  }

  {
    auto& make_specs = specs["jenoptik"];
    make_specs.reserve(48);
    make_specs.emplace_back("jd1300d", 6.4000f);
    make_specs.emplace_back("jd1300f", 6.4000f);
    make_specs.emplace_back("jd1500z3", 6.4000f);
    make_specs.emplace_back("jd2.1ff", 4.5000f);
    make_specs.emplace_back("jd2.1xz3", 4.5000f);
    make_specs.emplace_back("jd2100af", 5.3300f);
    make_specs.emplace_back("jd2100f", 5.3300f);
    make_specs.emplace_back("jd2100m", 5.3300f);
    make_specs.emplace_back("jd2100z3s", 5.3300f);
    make_specs.emplace_back("jd2300z3", 7.5300f);
    make_specs.emplace_back("jd3.1exclusiv", 6.4000f);
    make_specs.emplace_back("jd3.1z3mpeg4", 5.3300f);
    make_specs.emplace_back("jd3.3af", 5.3300f);
    make_specs.emplace_back("jd3.3xz3", 5.3300f);
    make_specs.emplace_back("jd3.3x4ie", 5.3300f);
    make_specs.emplace_back("jd3.3z10", 5.3300f);
    make_specs.emplace_back("jd3300z3s", 7.1100f);
    make_specs.emplace_back("jd3300z3", 7.1100f);
    make_specs.emplace_back("jd4.0lcd", 7.1100f);
    make_specs.emplace_back("jd4.1xz3", 5.7500f);
    make_specs.emplace_back("jd4.1z3mpeg4", 5.7500f);
    make_specs.emplace_back("jd4.1z8", 5.7500f);
    make_specs.emplace_back("jd4.1zoom", 5.7500f);
    make_specs.emplace_back("jd4100z3s", 7.1100f);
    make_specs.emplace_back("jd4100z3", 7.5300f);
    make_specs.emplace_back("jd4100zoom", 7.1100f);
    make_specs.emplace_back("jd4360z", 7.1100f);
    make_specs.emplace_back("jd4363z", 7.1100f);
    make_specs.emplace_back("jd5.0z3easyshot", 5.7500f);
    make_specs.emplace_back("jd5.2z3mpeg4", 7.1100f);
    make_specs.emplace_back("jd5.2z3", 7.1100f);
    make_specs.emplace_back("jd5.2zoom", 5.7500f);
    make_specs.emplace_back("jd5200z3", 7.1100f);
    make_specs.emplace_back("jd6.0z3exclusiv", 7.1100f);
    make_specs.emplace_back("jd6.0z3mpeg4", 7.1100f);
    make_specs.emplace_back("jd6.0z3", 7.1100f);
    make_specs.emplace_back("jd8.0exclusiv", 7.1100f);
    make_specs.emplace_back("jd8.0z3easyshot", 7.1100f);
    make_specs.emplace_back("jdc1.3lcd", 6.4000f);
    make_specs.emplace_back("jdc1.3sd", 6.4000f);
    make_specs.emplace_back("jdc1300", 6.4000f);
    make_specs.emplace_back("jdc2.1lcd", 6.4000f);
    make_specs.emplace_back("jdc3.0s", 6.4000f);
    make_specs.emplace_back("jdc3.1lcd", 6.4000f);
    make_specs.emplace_back("jdc3.1li", 6.4000f);
    make_specs.emplace_back("jdc3.1sl", 6.4000f);
    make_specs.emplace_back("jdc3.1z3", 6.4000f);
    make_specs.emplace_back("jdc5.0sl", 7.1100f);
  }

  {
    auto& make_specs = specs["jvc"];
    make_specs.reserve(2);
    make_specs.emplace_back("gcqx3hd", 7.1100f);
    make_specs.emplace_back("gcqx5hd", 7.1100f);
  }

  {
    auto& make_specs = specs["kodak"];
    make_specs.reserve(207);
    make_specs.emplace_back("dc200plus", 7.2700f);
    make_specs.emplace_back("dc200", 7.2700f);
    make_specs.emplace_back("dc210plus", 7.2700f);
    make_specs.emplace_back("dc215", 7.2700f);
    make_specs.emplace_back("dc220", 7.2700f);
    make_specs.emplace_back("dc240", 7.2700f);
    make_specs.emplace_back("dc260", 7.2700f);
    make_specs.emplace_back("dc265", 7.2700f);
    make_specs.emplace_back("dc280", 7.5300f);
    make_specs.emplace_back("dc290", 7.2700f);
    make_specs.emplace_back("dc3200", 7.5300f);
    make_specs.emplace_back("dc3400", 7.5300f);
    make_specs.emplace_back("dc3800", 7.5300f);
    make_specs.emplace_back("dc4800", 7.2700f);
    make_specs.emplace_back("dc5000", 7.2700f);
    make_specs.emplace_back("dcspro14n", 36.0000f);
    make_specs.emplace_back("dcsproslr/c", 36.0000f);
    make_specs.emplace_back("dcsproslr/n", 36.0000f);
    make_specs.emplace_back("dcs315", 27.6500f);
    make_specs.emplace_back("dcs330", 18.1000f);
    make_specs.emplace_back("dcs420", 14.0000f);
    make_specs.emplace_back("dcs460", 27.6500f);
    make_specs.emplace_back("dcs520", 27.6500f);
    make_specs.emplace_back("dcs560", 27.6500f);
    make_specs.emplace_back("dcs620x", 22.8000f);
    make_specs.emplace_back("dcs620", 27.6500f);
    make_specs.emplace_back("dcs660", 27.6500f);
    make_specs.emplace_back("dcs720x", 22.8000f);
    make_specs.emplace_back("dcs760", 27.6500f);
    make_specs.emplace_back("dx3215", 5.3300f);
    make_specs.emplace_back("dx3500", 6.4000f);
    make_specs.emplace_back("dx3600", 6.4000f);
    make_specs.emplace_back("dx3700", 7.1100f);
    make_specs.emplace_back("dx3900", 7.1100f);
    make_specs.emplace_back("dx4330", 5.7500f);
    make_specs.emplace_back("dx4530", 5.7500f);
    make_specs.emplace_back("dx4900", 5.7500f);
    make_specs.emplace_back("dx6340", 5.3300f);
    make_specs.emplace_back("dx6440", 5.7500f);
    make_specs.emplace_back("dx6490", 5.7500f);
    make_specs.emplace_back("dx7440", 5.7500f);
    make_specs.emplace_back("dx7590", 5.7500f);
    make_specs.emplace_back("dx7630", 7.1100f);
    make_specs.emplace_back("easysharec1013", 6.1600f);
    make_specs.emplace_back("easysharec135", 6.1600f);
    make_specs.emplace_back("easysharec140", 5.7500f);
    make_specs.emplace_back("easysharec142", 5.7500f);
    make_specs.emplace_back("easysharec143", 6.1600f);
    make_specs.emplace_back("easysharec1505", 6.1600f);
    make_specs.emplace_back("easysharec1530", 6.1600f);
    make_specs.emplace_back("easysharec1550", 6.1600f);
    make_specs.emplace_back("easysharec160", 5.7500f);
    make_specs.emplace_back("easysharec180", 6.1600f);
    make_specs.emplace_back("easysharec182", 6.1600f);
    make_specs.emplace_back("easysharec183", 6.1600f);
    make_specs.emplace_back("easysharec190", 6.1600f);
    make_specs.emplace_back("easysharec195", 6.1600f);
    make_specs.emplace_back("easysharec300", 7.1100f);
    make_specs.emplace_back("easysharec310", 7.1100f);
    make_specs.emplace_back("easysharec330", 7.1100f);
    make_specs.emplace_back("easysharec340", 7.1100f);
    make_specs.emplace_back("easysharec360", 7.1100f);
    make_specs.emplace_back("easysharec433", 5.7500f);
    make_specs.emplace_back("easysharec503", 5.7500f);
    make_specs.emplace_back("easysharec513", 5.7500f);
    make_specs.emplace_back("easysharec530", 5.7500f);
    make_specs.emplace_back("easysharec533", 5.7500f);
    make_specs.emplace_back("easysharec610", 5.7500f);
    make_specs.emplace_back("easysharec613", 5.7500f);
    make_specs.emplace_back("easysharec623", 5.7500f);
    make_specs.emplace_back("easysharec643", 5.7500f);
    make_specs.emplace_back("easysharec653", 5.7500f);
    make_specs.emplace_back("easysharec663", 5.7500f);
    make_specs.emplace_back("easysharec703", 5.7500f);
    make_specs.emplace_back("easysharec713", 5.7500f);
    make_specs.emplace_back("easysharec743", 5.7500f);
    make_specs.emplace_back("easysharec763", 5.7500f);
    make_specs.emplace_back("easysharec813", 5.7500f);
    make_specs.emplace_back("easysharec875", 7.1100f);
    make_specs.emplace_back("easysharec913", 5.7500f);
    make_specs.emplace_back("easysharecd1013", 6.1600f);
    make_specs.emplace_back("easysharecd703", 5.7500f);
    make_specs.emplace_back("easysharecd80", 6.1600f);
    make_specs.emplace_back("easysharecd82", 6.0800f);
    make_specs.emplace_back("easysharecd90", 6.0800f);
    make_specs.emplace_back("easysharecd93", 5.7500f);
    make_specs.emplace_back("easysharecx4200", 5.3300f);
    make_specs.emplace_back("easysharecx4230", 5.3300f);
    make_specs.emplace_back("easysharecx4300", 5.3300f);
    make_specs.emplace_back("easysharecx6200", 5.7500f);
    make_specs.emplace_back("easysharecx6230", 5.7500f);
    make_specs.emplace_back("easysharecx6330", 5.7500f);
    make_specs.emplace_back("easysharecx6445", 5.7500f);
    make_specs.emplace_back("easysharecx7220", 5.7500f);
    make_specs.emplace_back("easysharecx7300", 5.3300f);
    make_specs.emplace_back("easysharecx7330", 5.7500f);
    make_specs.emplace_back("easysharecx7430", 5.7500f);
    make_specs.emplace_back("easysharecx7525", 5.7500f);
    make_specs.emplace_back("easysharecx7530", 5.7500f);
    make_specs.emplace_back("easysharels745", 7.1100f);
    make_specs.emplace_back("easysharem1033", 6.1600f);
    make_specs.emplace_back("easysharem1063", 5.7500f);
    make_specs.emplace_back("easysharem1073is", 5.7500f);
    make_specs.emplace_back("easysharem1093is", 6.1600f);
    make_specs.emplace_back("easysharem215", 4.8000f);
    make_specs.emplace_back("easysharem320", 5.7500f);
    make_specs.emplace_back("easysharem340", 6.1600f);
    make_specs.emplace_back("easysharem341", 6.1600f);
    make_specs.emplace_back("easysharem380", 6.1600f);
    make_specs.emplace_back("easysharem381", 6.1600f);
    make_specs.emplace_back("easysharem420", 6.1600f);
    make_specs.emplace_back("easysharem522", 6.1600f);
    make_specs.emplace_back("easysharem530", 6.1600f);
    make_specs.emplace_back("easysharem531", 6.1600f);
    make_specs.emplace_back("easysharem532", 6.1600f);
    make_specs.emplace_back("easysharem5370", 6.1600f);
    make_specs.emplace_back("easysharem550", 6.1600f);
    make_specs.emplace_back("easysharem552", 6.1600f);
    make_specs.emplace_back("easysharem565", 6.1600f);
    make_specs.emplace_back("easysharem575", 6.1600f);
    make_specs.emplace_back("easysharem580", 6.1600f);
    make_specs.emplace_back("easysharem583", 6.0800f);
    make_specs.emplace_back("easysharem750", 6.1600f);
    make_specs.emplace_back("easysharem753", 5.7500f);
    make_specs.emplace_back("easysharem763", 5.7500f);
    make_specs.emplace_back("easysharem853", 5.7500f);
    make_specs.emplace_back("easysharem863", 5.7500f);
    make_specs.emplace_back("easysharem873", 5.7500f);
    make_specs.emplace_back("easysharem883", 5.7500f);
    make_specs.emplace_back("easysharem893is", 5.7500f);
    make_specs.emplace_back("easysharemaxz990", 6.0800f);
    make_specs.emplace_back("easysharemd1063", 6.0800f);
    make_specs.emplace_back("easysharemd30", 6.0800f);
    make_specs.emplace_back("easysharemd41", 6.0800f);
    make_specs.emplace_back("easysharemd81", 6.0800f);
    make_specs.emplace_back("easysharemd853", 5.7500f);
    make_specs.emplace_back("easysharemd863", 5.7500f);
    make_specs.emplace_back("easysharemini", 4.8000f);
    make_specs.emplace_back("easysharemx1063", 6.0800f);
    make_specs.emplace_back("easyshareone6mp", 5.7500f);
    make_specs.emplace_back("easyshareone", 5.7500f);
    make_specs.emplace_back("easysharep712", 5.7500f);
    make_specs.emplace_back("easysharep850", 5.7500f);
    make_specs.emplace_back("easysharep880", 7.1100f);
    make_specs.emplace_back("easysharesport", 6.1600f);
    make_specs.emplace_back("easysharetouchm577", 6.1600f);
    make_specs.emplace_back("easysharev1003", 7.1100f);
    make_specs.emplace_back("easysharev1073", 7.8500f);
    make_specs.emplace_back("easysharev1233", 7.4400f);
    make_specs.emplace_back("easysharev1253", 7.4400f);
    make_specs.emplace_back("easysharev1273", 7.4400f);
    make_specs.emplace_back("easysharev530", 5.7500f);
    make_specs.emplace_back("easysharev550", 5.7500f);
    make_specs.emplace_back("easysharev570", 5.7500f);
    make_specs.emplace_back("easysharev603", 5.7500f);
    make_specs.emplace_back("easysharev610", 5.7500f);
    make_specs.emplace_back("easysharev705", 5.7500f);
    make_specs.emplace_back("easysharev803", 7.1100f);
    make_specs.emplace_back("easysharez1012is", 6.0800f);
    make_specs.emplace_back("easysharez1015is", 6.0800f);
    make_specs.emplace_back("easysharez1085is", 7.8500f);
    make_specs.emplace_back("easysharez1275", 7.4400f);
    make_specs.emplace_back("easysharez1285", 7.4400f);
    make_specs.emplace_back("easysharez1485is", 7.4400f);
    make_specs.emplace_back("easysharez5010", 6.1600f);
    make_specs.emplace_back("easysharez5120", 6.0800f);
    make_specs.emplace_back("easysharez612", 5.7500f);
    make_specs.emplace_back("easysharez650", 5.7500f);
    make_specs.emplace_back("easysharez700", 5.7500f);
    make_specs.emplace_back("easysharez710", 5.7500f);
    make_specs.emplace_back("easysharez712is", 5.7500f);
    make_specs.emplace_back("easysharez730", 5.7500f);
    make_specs.emplace_back("easysharez740", 5.7500f);
    make_specs.emplace_back("easysharez7590", 5.7500f);
    make_specs.emplace_back("easysharez760", 7.1100f);
    make_specs.emplace_back("easysharez812is", 5.7500f);
    make_specs.emplace_back("easysharez8612is", 5.7500f);
    make_specs.emplace_back("easysharez885", 7.1100f);
    make_specs.emplace_back("easysharez915", 6.1600f);
    make_specs.emplace_back("easysharez950", 6.0800f);
    make_specs.emplace_back("easysharez980", 6.0800f);
    make_specs.emplace_back("easysharez981", 6.0800f);
    make_specs.emplace_back("easysharez990", 6.0800f);
    make_specs.emplace_back("easysharezd15", 6.1600f);
    make_specs.emplace_back("easysharezd710", 5.7500f);
    make_specs.emplace_back("easysharezd8612is", 5.7500f);
    make_specs.emplace_back("ls420", 7.1100f);
    make_specs.emplace_back("ls443", 5.3300f);
    make_specs.emplace_back("ls633", 5.7500f);
    make_specs.emplace_back("ls743", 7.1100f);
    make_specs.emplace_back("ls753", 7.1100f);
    make_specs.emplace_back("ls755", 5.7500f);
    make_specs.emplace_back("m590", 4.8000f);
    make_specs.emplace_back("mc3", 6.4000f);
    make_specs.emplace_back("pixproaz251", 6.1600f);
    make_specs.emplace_back("pixproaz361", 6.1600f);
    make_specs.emplace_back("pixproaz362", 6.1600f);
    make_specs.emplace_back("pixproaz501", 6.1600f);
    make_specs.emplace_back("pixproaz521", 6.1600f);
    make_specs.emplace_back("pixproaz522", 6.1600f);
    make_specs.emplace_back("pixproaz651", 6.1600f);
    make_specs.emplace_back("pixprofz151", 6.1600f);
    make_specs.emplace_back("pixprofz201", 6.1600f);
    make_specs.emplace_back("pixprofz41", 6.1600f);
    make_specs.emplace_back("pixprofz51", 6.1600f);
    make_specs.emplace_back("s1", 17.3000f);
    make_specs.emplace_back("slice", 6.1600f);
  }

  {
    auto& make_specs = specs["konica"];
    make_specs.reserve(18);
    make_specs.emplace_back("dg2", 5.3300f);
    make_specs.emplace_back("dg3z", 5.3300f);
    make_specs.emplace_back("qm100", 4.8000f);
    make_specs.emplace_back("qm200", 6.4000f);
    make_specs.emplace_back("revioc2", 4.2300f);
    make_specs.emplace_back("reviokd200z", 5.3300f);
    make_specs.emplace_back("reviokd210z", 7.1100f);
    make_specs.emplace_back("reviokd220z", 4.5000f);
    make_specs.emplace_back("reviokd25", 7.1100f);
    make_specs.emplace_back("reviokd300z", 7.1100f);
    make_specs.emplace_back("reviokd310z", 7.1100f);
    make_specs.emplace_back("reviokd3300z", 5.7500f);
    make_specs.emplace_back("reviokd4000z", 7.1100f);
    make_specs.emplace_back("reviokd400z", 7.1100f);
    make_specs.emplace_back("reviokd410z", 7.1100f);
    make_specs.emplace_back("reviokd420z", 5.7500f);
    make_specs.emplace_back("reviokd500z", 7.1100f);
    make_specs.emplace_back("reviokd510z", 7.1100f);
  }

  {
    auto& make_specs = specs["konica-minolta"];
    make_specs.reserve(24);
    make_specs.emplace_back("dg5w", 5.7500f);
    make_specs.emplace_back("dimagea200", 8.8000f);
    make_specs.emplace_back("dimagea2", 8.8000f);
    make_specs.emplace_back("dimagee40", 6.4000f);
    make_specs.emplace_back("dimagee500", 5.7500f);
    make_specs.emplace_back("dimagee50", 5.7500f);
    make_specs.emplace_back("dimageg530", 5.7500f);
    make_specs.emplace_back("dimageg600", 7.2700f);
    make_specs.emplace_back("dimagex1", 7.1100f);
    make_specs.emplace_back("dimagex31", 4.5000f);
    make_specs.emplace_back("dimagex50", 5.7500f);
    make_specs.emplace_back("dimagex60", 5.7500f);
    make_specs.emplace_back("dimagexg", 5.3300f);
    make_specs.emplace_back("dimagez10", 5.7500f);
    make_specs.emplace_back("dimagez20", 5.7500f);
    make_specs.emplace_back("dimagez2", 5.7500f);
    make_specs.emplace_back("dimagez3", 5.7500f);
    make_specs.emplace_back("dimagez5", 5.7500f);
    make_specs.emplace_back("dimagez6", 5.7500f);
    make_specs.emplace_back("dynax5d", 23.5000f);
    make_specs.emplace_back("dynax7d", 23.5000f);
    make_specs.emplace_back("eminid", 8.8000f);
    make_specs.emplace_back("eminim", 8.8000f);
    make_specs.emplace_back("emini", 8.8000f);
  }

  {
    auto& make_specs = specs["kyocera"];
    make_specs.reserve(17);
    make_specs.emplace_back("finecam3300", 7.1100f);
    make_specs.emplace_back("finecaml30", 5.3300f);
    make_specs.emplace_back("finecaml3v", 5.3300f);
    make_specs.emplace_back("finecaml3", 5.3300f);
    make_specs.emplace_back("finecaml4v", 7.1100f);
    make_specs.emplace_back("finecaml4", 5.7500f);
    make_specs.emplace_back("finecamm400r", 5.3300f);
    make_specs.emplace_back("finecamm410r", 5.3300f);
    make_specs.emplace_back("finecams3l", 7.1100f);
    make_specs.emplace_back("finecams3r", 7.1100f);
    make_specs.emplace_back("finecams3x", 7.1100f);
    make_specs.emplace_back("finecams3", 7.1100f);
    make_specs.emplace_back("finecams4", 7.1100f);
    make_specs.emplace_back("finecams5r", 7.1100f);
    make_specs.emplace_back("finecams5", 7.1100f);
    make_specs.emplace_back("finecamsl300r", 5.3300f);
    make_specs.emplace_back("finecamsl400r", 5.3300f);
  }

  {
    auto& make_specs = specs["leica"];
    make_specs.reserve(47);
    make_specs.emplace_back("clux1", 5.7500f);
    make_specs.emplace_back("clux2", 5.7500f);
    make_specs.emplace_back("clux3", 6.0800f);
    make_specs.emplace_back("c(typ112)", 7.5300f);
    make_specs.emplace_back("dlux2", 7.7600f);
    make_specs.emplace_back("dlux3", 7.7600f);
    make_specs.emplace_back("dlux4", 7.8500f);
    make_specs.emplace_back("dlux5", 7.8500f);
    make_specs.emplace_back("dlux6", 7.5300f);
    make_specs.emplace_back("dlux(typ109)", 17.3000f);
    make_specs.emplace_back("dlux", 5.7500f);
    make_specs.emplace_back("digilux1", 7.5300f);
    make_specs.emplace_back("digilux2", 8.8000f);
    make_specs.emplace_back("digilux3", 17.3000f);
    make_specs.emplace_back("digilux4.3", 7.5300f);
    make_specs.emplace_back("digiluxzoom", 6.4000f);
    make_specs.emplace_back("digilux", 6.4000f);
    make_specs.emplace_back("medition60", 36.0000f);
    make_specs.emplace_back("mmonochrom(typ246)", 35.8000f);
    make_specs.emplace_back("mmonochrom", 35.8000f);
    make_specs.emplace_back("mp", 36.0000f);
    make_specs.emplace_back("mtyp240", 36.0000f);
    make_specs.emplace_back("m8.2", 27.0000f);
    make_specs.emplace_back("m8", 27.0000f);
    make_specs.emplace_back("m9p", 35.8000f);
    make_specs.emplace_back("m9titanium", 35.8000f);
    make_specs.emplace_back("m9", 35.8000f);
    make_specs.emplace_back("metyp220", 35.8000f);
    make_specs.emplace_back("q(typ116)", 36.0000f);
    make_specs.emplace_back("se", 45.0000f);
    make_specs.emplace_back("s(type007)", 45.0000f);
    make_specs.emplace_back("s2", 45.0000f);
    make_specs.emplace_back("sl(typ601)", 36.0000f);
    make_specs.emplace_back("t(typ701)", 23.6000f);
    make_specs.emplace_back("vlux1", 7.1100f);
    make_specs.emplace_back("vlux20", 6.0800f);
    make_specs.emplace_back("vlux2", 6.0800f);
    make_specs.emplace_back("vlux30", 6.0800f);
    make_specs.emplace_back("vlux3", 6.0800f);
    make_specs.emplace_back("vlux40", 6.0800f);
    make_specs.emplace_back("vlux4", 6.1600f);
    make_specs.emplace_back("vlux(typ114)", 13.2000f);
    make_specs.emplace_back("x1", 23.6000f);
    make_specs.emplace_back("xe", 23.6000f);
    make_specs.emplace_back("x(typ113)", 23.6000f);
    make_specs.emplace_back("xvario", 23.6000f);
    make_specs.emplace_back("x2", 23.6000f);
  }

  {
    auto& make_specs = specs["minolta"];
    make_specs.reserve(27);
    make_specs.emplace_back("dimage2300", 7.5300f);
    make_specs.emplace_back("dimage2330", 7.5300f);
    make_specs.emplace_back("dimage5", 7.1100f);
    make_specs.emplace_back("dimage7hi", 8.8000f);
    make_specs.emplace_back("dimage7i", 8.8000f);
    make_specs.emplace_back("dimage7", 8.8000f);
    make_specs.emplace_back("dimagea1", 8.8000f);
    make_specs.emplace_back("dimagee201", 7.5300f);
    make_specs.emplace_back("dimagee203", 5.3300f);
    make_specs.emplace_back("dimagee223", 5.3300f);
    make_specs.emplace_back("dimagee323", 5.3300f);
    make_specs.emplace_back("dimageex1500wide", 6.4000f);
    make_specs.emplace_back("dimageex1500zoom", 6.4000f);
    make_specs.emplace_back("dimagef100", 7.1100f);
    make_specs.emplace_back("dimagef200", 7.1100f);
    make_specs.emplace_back("dimagef300", 7.1100f);
    make_specs.emplace_back("dimageg400", 5.7500f);
    make_specs.emplace_back("dimageg500", 7.1100f);
    make_specs.emplace_back("dimages304", 7.1100f);
    make_specs.emplace_back("dimages404", 7.1100f);
    make_specs.emplace_back("dimages414", 7.1100f);
    make_specs.emplace_back("dimagex20", 4.5000f);
    make_specs.emplace_back("dimagexi", 5.3300f);
    make_specs.emplace_back("dimagext", 5.3300f);
    make_specs.emplace_back("dimagex", 5.3300f);
    make_specs.emplace_back("dimagez1", 5.3300f);
    make_specs.emplace_back("rd3000", 6.4000f);
  }

  {
    auto& make_specs = specs["minox"];
    make_specs.reserve(48);
    make_specs.emplace_back("classicleicam32.1", 6.4000f);
    make_specs.emplace_back("classicleicam33mp", 6.4000f);
    make_specs.emplace_back("classicleicam34mp", 6.4000f);
    make_specs.emplace_back("classicleicam35mp", 6.4000f);
    make_specs.emplace_back("dc1011carat", 7.5300f);
    make_specs.emplace_back("dc1011", 7.5300f);
    make_specs.emplace_back("dc1022", 7.5300f);
    make_specs.emplace_back("dc1033", 5.7500f);
    make_specs.emplace_back("dc1044", 6.0800f);
    make_specs.emplace_back("dc1055", 6.0800f);
    make_specs.emplace_back("dc1211", 6.0800f);
    make_specs.emplace_back("dc1222", 6.1600f);
    make_specs.emplace_back("dc1233", 6.0800f);
    make_specs.emplace_back("dc1311", 5.3300f);
    make_specs.emplace_back("dc1422", 6.0800f);
    make_specs.emplace_back("dc2111", 5.3300f);
    make_specs.emplace_back("dc2122", 5.3300f);
    make_specs.emplace_back("dc2133", 4.5000f);
    make_specs.emplace_back("dc3311", 7.1100f);
    make_specs.emplace_back("dc4011", 7.1100f);
    make_specs.emplace_back("dc4211", 5.7500f);
    make_specs.emplace_back("dc5011", 5.7500f);
    make_specs.emplace_back("dc5211", 7.1100f);
    make_specs.emplace_back("dc5222", 5.7500f);
    make_specs.emplace_back("dc6011", 5.7500f);
    make_specs.emplace_back("dc6033wp", 5.7500f);
    make_specs.emplace_back("dc6211", 5.7500f);
    make_specs.emplace_back("dc6311", 7.1100f);
    make_specs.emplace_back("dc7011", 5.7500f);
    make_specs.emplace_back("dc7022", 5.7500f);
    make_specs.emplace_back("dc7411", 5.7500f);
    make_specs.emplace_back("dc8011", 5.7500f);
    make_specs.emplace_back("dc8022wp", 5.7500f);
    make_specs.emplace_back("dc8111", 7.1100f);
    make_specs.emplace_back("dc8122", 7.1100f);
    make_specs.emplace_back("dc9011wp", 6.1600f);
    make_specs.emplace_back("dcc14.0", 6.1600f);
    make_specs.emplace_back("dcc5.0whiteedition", 6.1600f);
    make_specs.emplace_back("dcc5.1", 6.1600f);
    make_specs.emplace_back("dccleicam35mpgold", 6.1600f);
    make_specs.emplace_back("dccrolleiflexaf5.0", 6.4000f);
    make_specs.emplace_back("dd1diamond", 6.4000f);
    make_specs.emplace_back("dd100", 6.4000f);
    make_specs.emplace_back("dd1", 6.4000f);
    make_specs.emplace_back("dd200", 6.4000f);
    make_specs.emplace_back("dm1", 6.4000f);
    make_specs.emplace_back("mobidv", 6.4000f);
    make_specs.emplace_back("rolleiflexminidigi", 6.4000f);
  }

  {
    auto& make_specs = specs["nikon"];
    make_specs.reserve(266);
    make_specs.emplace_back("1aw1", 13.2000f);
    make_specs.emplace_back("1j1", 13.2000f);
    make_specs.emplace_back("1j2", 13.2000f);
    make_specs.emplace_back("1j3", 13.2000f);
    make_specs.emplace_back("1j4", 13.2000f);
    make_specs.emplace_back("1j5", 13.2000f);
    make_specs.emplace_back("1s1", 13.2000f);
    make_specs.emplace_back("1s2", 13.2000f);
    make_specs.emplace_back("1v1", 13.2000f);
    make_specs.emplace_back("1v2", 13.2000f);
    make_specs.emplace_back("1v3", 13.2000f);
    make_specs.emplace_back("coolpix100", 4.8000f);
    make_specs.emplace_back("coolpix2000", 5.3300f);
    make_specs.emplace_back("coolpix2100", 4.5000f);
    make_specs.emplace_back("coolpix2200", 4.5000f);
    make_specs.emplace_back("coolpix2500", 5.3300f);
    make_specs.emplace_back("coolpix300", 4.8000f);
    make_specs.emplace_back("coolpix3100", 5.3300f);
    make_specs.emplace_back("coolpix3200", 5.3300f);
    make_specs.emplace_back("coolpix3500", 5.3300f);
    make_specs.emplace_back("coolpix3700", 5.3300f);
    make_specs.emplace_back("coolpix4100", 5.7500f);
    make_specs.emplace_back("coolpix4200", 7.1100f);
    make_specs.emplace_back("coolpix4300", 7.1100f);
    make_specs.emplace_back("coolpix4500", 7.1100f);
    make_specs.emplace_back("coolpix4600", 5.7500f);
    make_specs.emplace_back("coolpix4800", 5.7500f);
    make_specs.emplace_back("coolpix5000", 8.8000f);
    make_specs.emplace_back("coolpix5200", 7.1100f);
    make_specs.emplace_back("coolpix5400", 7.1100f);
    make_specs.emplace_back("coolpix5600", 5.7500f);
    make_specs.emplace_back("coolpix5700", 8.8000f);
    make_specs.emplace_back("coolpix5900", 7.1100f);
    make_specs.emplace_back("coolpix600", 5.3300f);
    make_specs.emplace_back("coolpix700", 6.4000f);
    make_specs.emplace_back("coolpix7600", 7.1100f);
    make_specs.emplace_back("coolpix775", 5.3300f);
    make_specs.emplace_back("coolpix7900", 7.1100f);
    make_specs.emplace_back("coolpix800", 6.4000f);
    make_specs.emplace_back("coolpix8400", 8.8000f);
    make_specs.emplace_back("coolpix8700", 8.8000f);
    make_specs.emplace_back("coolpix8800", 8.8000f);
    make_specs.emplace_back("coolpix880", 7.1100f);
    make_specs.emplace_back("coolpix885", 7.1100f);
    make_specs.emplace_back("coolpix900s", 5.3300f);
    make_specs.emplace_back("coolpix900", 5.3300f);
    make_specs.emplace_back("coolpix910", 6.4000f);
    make_specs.emplace_back("coolpix950", 6.4000f);
    make_specs.emplace_back("coolpix990", 7.1100f);
    make_specs.emplace_back("coolpix995", 7.1100f);
    make_specs.emplace_back("coolpixaw100s", 6.1600f);
    make_specs.emplace_back("coolpixaw100", 6.1600f);
    make_specs.emplace_back("coolpixaw110", 6.1600f);
    make_specs.emplace_back("coolpixaw120", 6.1600f);
    make_specs.emplace_back("coolpixaw130", 6.1600f);
    make_specs.emplace_back("coolpixa", 23.6000f);
    make_specs.emplace_back("coolpixl100", 6.0800f);
    make_specs.emplace_back("coolpixl101", 5.7500f);
    make_specs.emplace_back("coolpixl10", 5.7500f);
    make_specs.emplace_back("coolpixl110", 6.1600f);
    make_specs.emplace_back("coolpixl11", 5.7500f);
    make_specs.emplace_back("coolpixl120", 6.1600f);
    make_specs.emplace_back("coolpixl12", 5.7500f);
    make_specs.emplace_back("coolpixl14", 5.7500f);
    make_specs.emplace_back("coolpixl15", 5.7500f);
    make_specs.emplace_back("coolpixl16", 5.7500f);
    make_specs.emplace_back("coolpixl18", 5.7500f);
    make_specs.emplace_back("coolpixl19", 5.7500f);
    make_specs.emplace_back("coolpixl1", 5.7500f);
    make_specs.emplace_back("coolpixl20", 6.0800f);
    make_specs.emplace_back("coolpixl21", 6.0800f);
    make_specs.emplace_back("coolpixl22", 6.1600f);
    make_specs.emplace_back("coolpixl23", 4.9600f);
    make_specs.emplace_back("coolpixl24", 6.1600f);
    make_specs.emplace_back("coolpixl25", 4.8000f);
    make_specs.emplace_back("coolpixl26", 6.1600f);
    make_specs.emplace_back("coolpixl27", 6.1600f);
    make_specs.emplace_back("coolpixl28", 6.1600f);
    make_specs.emplace_back("coolpixl29", 6.1600f);
    make_specs.emplace_back("coolpixl30", 6.1600f);
    make_specs.emplace_back("coolpixl310", 6.1600f);
    make_specs.emplace_back("coolpixl31", 6.1600f);
    make_specs.emplace_back("coolpixl320", 6.1600f);
    make_specs.emplace_back("coolpixl32", 6.1600f);
    make_specs.emplace_back("coolpixl330", 6.1600f);
    make_specs.emplace_back("coolpixl5", 5.7500f);
    make_specs.emplace_back("coolpixl610", 6.1600f);
    make_specs.emplace_back("coolpixl620", 6.1600f);
    make_specs.emplace_back("coolpixl6", 5.7500f);
    make_specs.emplace_back("coolpixl810", 6.1600f);
    make_specs.emplace_back("coolpixl820", 6.1600f);
    make_specs.emplace_back("coolpixl830", 6.1600f);
    make_specs.emplace_back("coolpixl840", 6.1600f);
    make_specs.emplace_back("coolpixp100", 6.1600f);
    make_specs.emplace_back("coolpixp1", 7.1100f);
    make_specs.emplace_back("coolpixp2", 7.1100f);
    make_specs.emplace_back("coolpixp300", 6.1600f);
    make_specs.emplace_back("coolpixp310", 6.1600f);
    make_specs.emplace_back("coolpixp330", 7.5300f);
    make_specs.emplace_back("coolpixp340", 7.5300f);
    make_specs.emplace_back("coolpixp3", 7.1100f);
    make_specs.emplace_back("coolpixp4", 7.1100f);
    make_specs.emplace_back("coolpixp5000", 7.1100f);
    make_specs.emplace_back("coolpixp500", 6.1600f);
    make_specs.emplace_back("coolpixp50", 5.7500f);
    make_specs.emplace_back("coolpixp5100", 7.4400f);
    make_specs.emplace_back("coolpixp510", 6.1600f);
    make_specs.emplace_back("coolpixp520", 6.1600f);
    make_specs.emplace_back("coolpixp530", 6.1600f);
    make_specs.emplace_back("coolpixp6000", 7.4400f);
    make_specs.emplace_back("coolpixp600", 6.1600f);
    make_specs.emplace_back("coolpixp60", 5.7500f);
    make_specs.emplace_back("coolpixp610", 6.1600f);
    make_specs.emplace_back("coolpixp7000", 7.5300f);
    make_specs.emplace_back("coolpixp7100", 7.5300f);
    make_specs.emplace_back("coolpixp7700", 7.5300f);
    make_specs.emplace_back("coolpixp7800", 7.5300f);
    make_specs.emplace_back("coolpixp80", 6.0800f);
    make_specs.emplace_back("coolpixp900", 6.1600f);
    make_specs.emplace_back("coolpixp90", 6.0800f);
    make_specs.emplace_back("coolpixs01", 4.9600f);
    make_specs.emplace_back("coolpixs02", 4.8000f);
    make_specs.emplace_back("coolpixs1000pj", 6.1600f);
    make_specs.emplace_back("coolpixs100", 6.1600f);
    make_specs.emplace_back("coolpixs10", 5.7500f);
    make_specs.emplace_back("coolpixs1100pj", 6.1600f);
    make_specs.emplace_back("coolpixs1200pj", 6.1600f);
    make_specs.emplace_back("coolpixs1", 5.7500f);
    make_specs.emplace_back("coolpixs200", 5.7500f);
    make_specs.emplace_back("coolpixs210", 5.7500f);
    make_specs.emplace_back("coolpixs220", 6.0800f);
    make_specs.emplace_back("coolpixs225", 6.0800f);
    make_specs.emplace_back("coolpixs230", 6.0800f);
    make_specs.emplace_back("coolpixs2500", 6.1600f);
    make_specs.emplace_back("coolpixs2600", 6.1600f);
    make_specs.emplace_back("coolpixs2700", 6.1600f);
    make_specs.emplace_back("coolpixs2750", 6.1600f);
    make_specs.emplace_back("coolpixs2800", 6.1600f);
    make_specs.emplace_back("coolpixs2900", 6.1600f);
    make_specs.emplace_back("coolpixs2", 5.7500f);
    make_specs.emplace_back("coolpixs3000", 6.1600f);
    make_specs.emplace_back("coolpixs30", 4.8000f);
    make_specs.emplace_back("coolpixs3100", 6.1600f);
    make_specs.emplace_back("coolpixs31", 4.9600f);
    make_specs.emplace_back("coolpixs3200", 6.1600f);
    make_specs.emplace_back("coolpixs32", 4.8000f);
    make_specs.emplace_back("coolpixs3300", 6.1600f);
    make_specs.emplace_back("coolpixs33", 4.8000f);
    make_specs.emplace_back("coolpixs3400", 6.1600f);
    make_specs.emplace_back("coolpixs3500", 6.1600f);
    make_specs.emplace_back("coolpixs3600", 6.1600f);
    make_specs.emplace_back("coolpixs3700", 6.1600f);
    make_specs.emplace_back("coolpixs3", 5.7500f);
    make_specs.emplace_back("coolpixs4000", 6.1600f);
    make_specs.emplace_back("coolpixs4100", 6.1600f);
    make_specs.emplace_back("coolpixs4150", 6.1600f);
    make_specs.emplace_back("coolpixs4200", 6.1600f);
    make_specs.emplace_back("coolpixs4300", 6.1600f);
    make_specs.emplace_back("coolpixs4400", 6.1600f);
    make_specs.emplace_back("coolpixs4", 5.7500f);
    make_specs.emplace_back("coolpixs500", 5.7500f);
    make_specs.emplace_back("coolpixs50c", 5.7500f);
    make_specs.emplace_back("coolpixs50", 5.7500f);
    make_specs.emplace_back("coolpixs5100", 6.1600f);
    make_specs.emplace_back("coolpixs510", 5.7500f);
    make_specs.emplace_back("coolpixs51c", 5.7500f);
    make_specs.emplace_back("coolpixs51", 5.7500f);
    make_specs.emplace_back("coolpixs5200", 6.1600f);
    make_specs.emplace_back("coolpixs520", 5.7500f);
    make_specs.emplace_back("coolpixs52c", 5.7500f);
    make_specs.emplace_back("coolpixs52", 5.7500f);
    make_specs.emplace_back("coolpixs5300", 6.1600f);
    make_specs.emplace_back("coolpixs550", 6.1600f);
    make_specs.emplace_back("coolpixs560", 6.0800f);
    make_specs.emplace_back("coolpixs570", 6.1600f);
    make_specs.emplace_back("coolpixs5", 5.7500f);
    make_specs.emplace_back("coolpixs6000", 6.1600f);
    make_specs.emplace_back("coolpixs600", 6.0800f);
    make_specs.emplace_back("coolpixs60", 6.1600f);
    make_specs.emplace_back("coolpixs6100", 6.1600f);
    make_specs.emplace_back("coolpixs610c", 6.0800f);
    make_specs.emplace_back("coolpixs610", 6.0800f);
    make_specs.emplace_back("coolpixs6150", 6.1600f);
    make_specs.emplace_back("coolpixs6200", 6.1600f);
    make_specs.emplace_back("coolpixs620", 6.0800f);
    make_specs.emplace_back("coolpixs6300", 6.1600f);
    make_specs.emplace_back("coolpixs630", 6.0800f);
    make_specs.emplace_back("coolpixs6400", 6.1600f);
    make_specs.emplace_back("coolpixs640", 6.0800f);
    make_specs.emplace_back("coolpixs6500", 6.1600f);
    make_specs.emplace_back("coolpixs6600", 6.1600f);
    make_specs.emplace_back("coolpixs6700", 6.1600f);
    make_specs.emplace_back("coolpixs6800", 6.1600f);
    make_specs.emplace_back("coolpixs6900", 6.1600f);
    make_specs.emplace_back("coolpixs6", 5.7500f);
    make_specs.emplace_back("coolpixs7000", 6.1600f);
    make_specs.emplace_back("coolpixs700", 7.4400f);
    make_specs.emplace_back("coolpixs70", 6.1600f);
    make_specs.emplace_back("coolpixs710", 7.4400f);
    make_specs.emplace_back("coolpixs7c", 5.7500f);
    make_specs.emplace_back("coolpixs8000", 6.1600f);
    make_specs.emplace_back("coolpixs800c", 6.1600f);
    make_specs.emplace_back("coolpixs80", 6.1600f);
    make_specs.emplace_back("coolpixs8100", 6.1600f);
    make_specs.emplace_back("coolpixs810c", 6.1600f);
    make_specs.emplace_back("coolpixs8200", 6.1600f);
    make_specs.emplace_back("coolpixs9050", 6.1600f);
    make_specs.emplace_back("coolpixs9100", 6.1600f);
    make_specs.emplace_back("coolpixs9200", 6.1600f);
    make_specs.emplace_back("coolpixs9300", 6.1600f);
    make_specs.emplace_back("coolpixs9400", 6.1600f);
    make_specs.emplace_back("coolpixs9500", 6.1600f);
    make_specs.emplace_back("coolpixs9600", 6.1600f);
    make_specs.emplace_back("coolpixs9700", 6.1600f);
    make_specs.emplace_back("coolpixs9900", 6.1600f);
    make_specs.emplace_back("coolpixs9", 5.7500f);
    make_specs.emplace_back("coolpixsq", 5.3300f);
    make_specs.emplace_back("d100", 23.7000f);
    make_specs.emplace_back("d1h", 23.7000f);
    make_specs.emplace_back("d1x", 23.7000f);
    make_specs.emplace_back("d1", 23.7000f);
    make_specs.emplace_back("d200", 23.6000f);
    make_specs.emplace_back("d2hs", 23.2000f);
    make_specs.emplace_back("d2h", 23.7000f);
    make_specs.emplace_back("d2xs", 23.7000f);
    make_specs.emplace_back("d2x", 23.7000f);
    make_specs.emplace_back("d3000", 23.6000f);
    make_specs.emplace_back("d300s", 23.6000f);
    make_specs.emplace_back("d300", 23.6000f);
    make_specs.emplace_back("d3100", 23.1000f);
    make_specs.emplace_back("d3200", 23.2000f);
    make_specs.emplace_back("d3300", 23.5000f);
    make_specs.emplace_back("d3s", 36.0000f);
    make_specs.emplace_back("d3x", 35.9000f);
    make_specs.emplace_back("d3", 36.0000f);
    make_specs.emplace_back("d40x", 23.6000f);
    make_specs.emplace_back("d40", 23.7000f);
    make_specs.emplace_back("d4s", 36.0000f);
    make_specs.emplace_back("d4", 36.0000f);
    make_specs.emplace_back("d5000", 23.6000f);
    make_specs.emplace_back("d50", 23.7000f);
    make_specs.emplace_back("d5100", 23.6000f);
    make_specs.emplace_back("d5200", 23.5000f);
    make_specs.emplace_back("d5300", 23.5000f);
    make_specs.emplace_back("d5500", 23.5000f);
    make_specs.emplace_back("d600", 35.9000f);
    make_specs.emplace_back("d60", 23.6000f);
    make_specs.emplace_back("d610", 35.9000f);
    make_specs.emplace_back("d7000", 23.6000f);
    make_specs.emplace_back("d700", 36.0000f);
    make_specs.emplace_back("d70s", 23.7000f);
    make_specs.emplace_back("d70", 23.7000f);
    make_specs.emplace_back("d7100", 23.5000f);
    make_specs.emplace_back("d7200", 23.5000f);
    make_specs.emplace_back("d750", 35.9000f);
    make_specs.emplace_back("d800e", 35.9000f);
    make_specs.emplace_back("d800", 35.9000f);
    make_specs.emplace_back("d80", 23.6000f);
    make_specs.emplace_back("d810", 35.9000f);
    make_specs.emplace_back("d90", 23.6000f);
    make_specs.emplace_back("df", 36.0000f);
    make_specs.emplace_back("e2ns", 8.8000f);
    make_specs.emplace_back("e2n", 8.8000f);
    make_specs.emplace_back("e2s", 8.8000f);
    make_specs.emplace_back("e3s", 8.8000f);
    make_specs.emplace_back("e3", 8.8000f);
  }

  {
    auto& make_specs = specs["nokia"];
    make_specs.reserve(2);
    make_specs.emplace_back("808pureview", 10.8200f);
    make_specs.emplace_back("lumia1020", 8.6400f);
  }

  {
    auto& make_specs = specs["olympus"];
    make_specs.reserve(314);
    make_specs.emplace_back("aira01", 17.3000f);
    make_specs.emplace_back("az1ferrari2004", 5.3300f);
    make_specs.emplace_back("az1", 5.3300f);
    make_specs.emplace_back("az2zoom", 5.3300f);
    make_specs.emplace_back("c1zoom", 4.5000f);
    make_specs.emplace_back("c1000l", 6.4000f);
    make_specs.emplace_back("c100", 4.5000f);
    make_specs.emplace_back("c120", 4.5000f);
    make_specs.emplace_back("c1400l", 8.8000f);
    make_specs.emplace_back("c1400xl", 8.8000f);
    make_specs.emplace_back("c150", 4.5000f);
    make_specs.emplace_back("c160", 5.3300f);
    make_specs.emplace_back("c170", 5.7500f);
    make_specs.emplace_back("c180", 5.7500f);
    make_specs.emplace_back("c1", 4.5000f);
    make_specs.emplace_back("c200zoom", 5.3300f);
    make_specs.emplace_back("c2000zoom", 6.4000f);
    make_specs.emplace_back("c2020zoom", 6.4000f);
    make_specs.emplace_back("c2040zoom", 6.4000f);
    make_specs.emplace_back("c2100uz", 6.4000f);
    make_specs.emplace_back("c21", 6.4000f);
    make_specs.emplace_back("c220zoom", 4.5000f);
    make_specs.emplace_back("c2500l", 8.8000f);
    make_specs.emplace_back("c2", 5.3300f);
    make_specs.emplace_back("c300zoom", 5.7500f);
    make_specs.emplace_back("c3000zoom", 7.1100f);
    make_specs.emplace_back("c3020zoom", 7.1100f);
    make_specs.emplace_back("c3030zoom", 7.1100f);
    make_specs.emplace_back("c3040zoom", 7.1100f);
    make_specs.emplace_back("c310zoom", 5.3300f);
    make_specs.emplace_back("c315zoom", 5.7500f);
    make_specs.emplace_back("c350zoom", 5.7500f);
    make_specs.emplace_back("c360zoom", 5.7500f);
    make_specs.emplace_back("c370zoom", 5.3300f);
    make_specs.emplace_back("c40zoom", 7.1100f);
    make_specs.emplace_back("c4000zoom", 7.1100f);
    make_specs.emplace_back("c4040zoom", 7.1100f);
    make_specs.emplace_back("c450zoom", 5.7500f);
    make_specs.emplace_back("c460zoomdelsol", 5.7500f);
    make_specs.emplace_back("c470zoom", 5.7500f);
    make_specs.emplace_back("c480zoom", 5.7500f);
    make_specs.emplace_back("c50zoom", 7.1100f);
    make_specs.emplace_back("c5000zoom", 7.2700f);
    make_specs.emplace_back("c5050zoom", 7.1100f);
    make_specs.emplace_back("c5060widezoom", 7.1100f);
    make_specs.emplace_back("c55zoom", 7.1100f);
    make_specs.emplace_back("c5500sportzoom", 7.1100f);
    make_specs.emplace_back("c60zoom", 7.2700f);
    make_specs.emplace_back("c70zoom", 7.1100f);
    make_specs.emplace_back("c700uz", 5.3300f);
    make_specs.emplace_back("c7000zoom", 7.1100f);
    make_specs.emplace_back("c7070widezoom", 7.1100f);
    make_specs.emplace_back("c720uz", 5.7500f);
    make_specs.emplace_back("c730uz", 5.3300f);
    make_specs.emplace_back("c740uz", 5.7500f);
    make_specs.emplace_back("c750uz", 5.3300f);
    make_specs.emplace_back("c760uz", 5.3300f);
    make_specs.emplace_back("c765uz", 5.7500f);
    make_specs.emplace_back("c770uz", 5.7500f);
    make_specs.emplace_back("c8080widezoom", 8.8000f);
    make_specs.emplace_back("c820l", 4.8000f);
    make_specs.emplace_back("c840l", 5.3300f);
    make_specs.emplace_back("c860l", 5.3300f);
    make_specs.emplace_back("c900zoom", 5.3300f);
    make_specs.emplace_back("c920zoom", 5.3300f);
    make_specs.emplace_back("c960zoom", 5.3300f);
    make_specs.emplace_back("c990zoom", 5.3300f);
    make_specs.emplace_back("d150z", 4.5000f);
    make_specs.emplace_back("d200l", 8.8000f);
    make_specs.emplace_back("d300l", 8.8000f);
    make_specs.emplace_back("d340l", 8.8000f);
    make_specs.emplace_back("d340r", 6.4000f);
    make_specs.emplace_back("d360l", 7.1100f);
    make_specs.emplace_back("d370", 4.5000f);
    make_specs.emplace_back("d380", 4.5000f);
    make_specs.emplace_back("d390", 4.5000f);
    make_specs.emplace_back("d395", 5.3300f);
    make_specs.emplace_back("d40zoom", 7.1100f);
    make_specs.emplace_back("d400zoom", 6.4000f);
    make_specs.emplace_back("d425", 5.7500f);
    make_specs.emplace_back("d435", 5.7500f);
    make_specs.emplace_back("d450zoom", 5.3300f);
    make_specs.emplace_back("d460zoom", 7.1100f);
    make_specs.emplace_back("d490zoom", 5.3300f);
    make_specs.emplace_back("d500l", 8.8000f);
    make_specs.emplace_back("d510zoom", 5.3300f);
    make_specs.emplace_back("d520zoom", 4.5000f);
    make_specs.emplace_back("d535zoom", 5.3300f);
    make_specs.emplace_back("d540zoom", 5.3300f);
    make_specs.emplace_back("d545zoom", 5.7500f);
    make_specs.emplace_back("d560zoom", 5.7500f);
    make_specs.emplace_back("d580zoom", 5.3300f);
    make_specs.emplace_back("d595zoom", 5.7500f);
    make_specs.emplace_back("d600l", 8.8000f);
    make_specs.emplace_back("d620l", 8.8000f);
    make_specs.emplace_back("d630zoom", 5.7500f);
    make_specs.emplace_back("e100rs", 6.4000f);
    make_specs.emplace_back("e10", 8.8000f);
    make_specs.emplace_back("e1", 17.3000f);
    make_specs.emplace_back("e20", 8.8000f);
    make_specs.emplace_back("e30", 17.3000f);
    make_specs.emplace_back("e3", 17.3000f);
    make_specs.emplace_back("e400", 17.3000f);
    make_specs.emplace_back("e410/evolte410", 17.3000f);
    make_specs.emplace_back("e420", 17.3000f);
    make_specs.emplace_back("e450", 17.3000f);
    make_specs.emplace_back("e510/evolte510", 17.3000f);
    make_specs.emplace_back("e520", 17.3000f);
    make_specs.emplace_back("e5", 17.3000f);
    make_specs.emplace_back("e600", 17.3000f);
    make_specs.emplace_back("e620", 17.3000f);
    make_specs.emplace_back("e300/evolte300", 17.3000f);
    make_specs.emplace_back("e500/evolte500", 17.3000f);
    make_specs.emplace_back("fe100", 5.7500f);
    make_specs.emplace_back("fe110", 5.7500f);
    make_specs.emplace_back("fe115", 5.7500f);
    make_specs.emplace_back("fe120", 5.7500f);
    make_specs.emplace_back("fe130", 5.7500f);
    make_specs.emplace_back("fe140", 5.7500f);
    make_specs.emplace_back("fe150", 5.7500f);
    make_specs.emplace_back("fe160", 5.7500f);
    make_specs.emplace_back("fe170", 5.7500f);
    make_specs.emplace_back("fe180", 5.7500f);
    make_specs.emplace_back("fe190", 5.7500f);
    make_specs.emplace_back("fe200", 5.7500f);
    make_specs.emplace_back("fe20", 6.0300f);
    make_specs.emplace_back("fe210", 5.7500f);
    make_specs.emplace_back("fe220", 5.7500f);
    make_specs.emplace_back("fe230", 5.7500f);
    make_specs.emplace_back("fe240", 5.7500f);
    make_specs.emplace_back("fe250", 7.1100f);
    make_specs.emplace_back("fe25", 6.0800f);
    make_specs.emplace_back("fe26", 6.0800f);
    make_specs.emplace_back("fe270", 5.7500f);
    make_specs.emplace_back("fe280", 6.1600f);
    make_specs.emplace_back("fe290", 5.7500f);
    make_specs.emplace_back("fe3000", 6.0800f);
    make_specs.emplace_back("fe300", 7.4400f);
    make_specs.emplace_back("fe3010", 6.0800f);
    make_specs.emplace_back("fe310", 5.7500f);
    make_specs.emplace_back("fe340", 6.0300f);
    make_specs.emplace_back("fe350", 5.7500f);
    make_specs.emplace_back("fe360", 6.0300f);
    make_specs.emplace_back("fe370", 6.0300f);
    make_specs.emplace_back("fe4000", 6.1600f);
    make_specs.emplace_back("fe4010", 6.1600f);
    make_specs.emplace_back("fe4020", 6.1600f);
    make_specs.emplace_back("fe4030", 6.0800f);
    make_specs.emplace_back("fe4040", 6.0800f);
    make_specs.emplace_back("fe4050", 6.1600f);
    make_specs.emplace_back("fe45", 6.0800f);
    make_specs.emplace_back("fe47", 6.0800f);
    make_specs.emplace_back("fe48", 6.1600f);
    make_specs.emplace_back("fe5000", 6.0800f);
    make_specs.emplace_back("fe5010", 6.0800f);
    make_specs.emplace_back("fe5020", 6.1600f);
    make_specs.emplace_back("fe5030", 6.0800f);
    make_specs.emplace_back("fe5035", 6.0800f);
    make_specs.emplace_back("fe5040", 6.1600f);
    make_specs.emplace_back("fe5050", 6.1600f);
    make_specs.emplace_back("ir300", 5.7500f);
    make_specs.emplace_back("ir500", 5.3300f);
    make_specs.emplace_back("m:robemr500i", 3.2000f);
    make_specs.emplace_back("mju1060", 6.0800f);
    make_specs.emplace_back("mju400digitalferrari", 5.7500f);
    make_specs.emplace_back("mju5000", 6.0800f);
    make_specs.emplace_back("mju7050", 6.1600f);
    make_specs.emplace_back("mju800black", 7.1100f);
    make_specs.emplace_back("mjuminidigitals", 5.7500f);
    make_specs.emplace_back("mjuminidigital", 5.7500f);
    make_specs.emplace_back("omdem10ii", 17.3000f);
    make_specs.emplace_back("omdem10", 17.3000f);
    make_specs.emplace_back("omdem1", 17.3000f);
    make_specs.emplace_back("omdem5markii", 17.3000f);
    make_specs.emplace_back("omdem5", 17.3000f);
    make_specs.emplace_back("penep1", 17.3000f);
    make_specs.emplace_back("penep2", 17.3000f);
    make_specs.emplace_back("penep3", 17.3000f);
    make_specs.emplace_back("penep5", 17.3000f);
    make_specs.emplace_back("penepl1s", 17.3000f);
    make_specs.emplace_back("penepl1", 17.3000f);
    make_specs.emplace_back("penepl2", 17.3000f);
    make_specs.emplace_back("penepl3", 17.3000f);
    make_specs.emplace_back("penepl5", 17.3000f);
    make_specs.emplace_back("penepl6", 17.3000f);
    make_specs.emplace_back("penepl7", 17.3000f);
    make_specs.emplace_back("penepm1", 17.3000f);
    make_specs.emplace_back("penepm2", 17.3000f);
    make_specs.emplace_back("sh21", 6.1600f);
    make_specs.emplace_back("sh25mr", 6.1600f);
    make_specs.emplace_back("sh50ihs", 6.1600f);
    make_specs.emplace_back("sp100", 6.1600f);
    make_specs.emplace_back("sp310", 7.1100f);
    make_specs.emplace_back("sp320", 7.1100f);
    make_specs.emplace_back("sp350", 7.1100f);
    make_specs.emplace_back("sp500uz", 5.7500f);
    make_specs.emplace_back("sp510uz", 5.7500f);
    make_specs.emplace_back("sp550uz", 5.7500f);
    make_specs.emplace_back("sp560uz", 6.1600f);
    make_specs.emplace_back("sp565uz", 6.0800f);
    make_specs.emplace_back("sp570uz", 6.0800f);
    make_specs.emplace_back("sp590uz", 6.0800f);
    make_specs.emplace_back("sp600uz", 6.0800f);
    make_specs.emplace_back("sp610uz", 6.1600f);
    make_specs.emplace_back("sp620uz", 6.1600f);
    make_specs.emplace_back("sp700", 5.7500f);
    make_specs.emplace_back("sp720uz", 6.1600f);
    make_specs.emplace_back("sp800uz", 6.0800f);
    make_specs.emplace_back("sp810uz", 6.1600f);
    make_specs.emplace_back("stylus1000", 7.1100f);
    make_specs.emplace_back("stylus1010", 6.0800f);
    make_specs.emplace_back("stylus1020", 6.0800f);
    make_specs.emplace_back("stylus1030sw", 6.0800f);
    make_specs.emplace_back("stylus1040", 6.0800f);
    make_specs.emplace_back("stylus1050sw", 6.0800f);
    make_specs.emplace_back("stylus1200", 7.4400f);
    make_specs.emplace_back("stylus1s", 7.5300f);
    make_specs.emplace_back("stylus1", 7.5300f);
    make_specs.emplace_back("stylus300", 5.7500f);
    make_specs.emplace_back("stylus400", 5.7500f);
    make_specs.emplace_back("stylus410", 5.7500f);
    make_specs.emplace_back("stylus500", 5.7500f);
    make_specs.emplace_back("stylus5010", 6.0800f);
    make_specs.emplace_back("stylus550wp", 6.0800f);
    make_specs.emplace_back("stylus600", 5.7500f);
    make_specs.emplace_back("stylus7000", 6.0800f);
    make_specs.emplace_back("stylus700", 6.1600f);
    make_specs.emplace_back("stylus7010", 6.0800f);
    make_specs.emplace_back("stylus7030", 6.0800f);
    make_specs.emplace_back("stylus7040", 6.0800f);
    make_specs.emplace_back("stylus720sw", 6.1600f);
    make_specs.emplace_back("stylus725sw", 6.1600f);
    make_specs.emplace_back("stylus730", 6.1600f);
    make_specs.emplace_back("stylus740", 6.1600f);
    make_specs.emplace_back("stylus750", 6.1600f);
    make_specs.emplace_back("stylus760", 6.1600f);
    make_specs.emplace_back("stylus770sw", 6.1600f);
    make_specs.emplace_back("stylus780", 6.1600f);
    make_specs.emplace_back("stylus790sw", 6.1600f);
    make_specs.emplace_back("stylus800", 7.1100f);
    make_specs.emplace_back("stylus810", 7.1100f);
    make_specs.emplace_back("stylus820", 6.1600f);
    make_specs.emplace_back("stylus830", 6.1600f);
    make_specs.emplace_back("stylus840", 6.0300f);
    make_specs.emplace_back("stylus850sw", 6.0300f);
    make_specs.emplace_back("stylus9000", 6.0800f);
    make_specs.emplace_back("stylus9010", 6.0800f);
    make_specs.emplace_back("stylussh1", 6.1600f);
    make_specs.emplace_back("stylussh2", 6.1600f);
    make_specs.emplace_back("stylussp820uz", 6.1600f);
    make_specs.emplace_back("stylustough3000", 6.0800f);
    make_specs.emplace_back("stylustough6000", 6.1600f);
    make_specs.emplace_back("stylustough6010", 6.1600f);
    make_specs.emplace_back("stylustough6020", 6.0800f);
    make_specs.emplace_back("stylustough8000", 6.0800f);
    make_specs.emplace_back("stylustough8010", 6.0800f);
    make_specs.emplace_back("stylusverves", 5.7500f);
    make_specs.emplace_back("stylusverve", 5.7500f);
    make_specs.emplace_back("stylusxz10", 6.1600f);
    make_specs.emplace_back("sz10", 6.1600f);
    make_specs.emplace_back("sz11", 6.1600f);
    make_specs.emplace_back("sz12", 6.1600f);
    make_specs.emplace_back("sz14", 6.1600f);
    make_specs.emplace_back("sz15", 6.1600f);
    make_specs.emplace_back("sz16", 6.1600f);
    make_specs.emplace_back("sz20", 6.1600f);
    make_specs.emplace_back("sz30mr", 6.1600f);
    make_specs.emplace_back("sz31mrihs", 6.1600f);
    make_specs.emplace_back("t100", 6.1600f);
    make_specs.emplace_back("t10", 6.1600f);
    make_specs.emplace_back("t110", 6.1600f);
    make_specs.emplace_back("tg310", 6.1600f);
    make_specs.emplace_back("tg320", 6.1600f);
    make_specs.emplace_back("tg610", 6.1600f);
    make_specs.emplace_back("tg630ihs", 6.1600f);
    make_specs.emplace_back("tg810", 6.1600f);
    make_specs.emplace_back("tg820ihs", 6.1600f);
    make_specs.emplace_back("tg830ihs", 6.1600f);
    make_specs.emplace_back("tg850ihs", 6.1600f);
    make_specs.emplace_back("toughtg1ihs", 6.1600f);
    make_specs.emplace_back("toughtg2ihs", 6.1600f);
    make_specs.emplace_back("toughtg3", 6.1600f);
    make_specs.emplace_back("toughtg4", 6.1600f);
    make_specs.emplace_back("toughtg620", 6.1600f);
    make_specs.emplace_back("toughtg860", 6.1600f);
    make_specs.emplace_back("vg110", 6.1600f);
    make_specs.emplace_back("vg120", 6.1600f);
    make_specs.emplace_back("vg130", 6.1600f);
    make_specs.emplace_back("vg145", 6.1600f);
    make_specs.emplace_back("vg150", 6.1600f);
    make_specs.emplace_back("vg160", 6.1600f);
    make_specs.emplace_back("vg165", 6.1600f);
    make_specs.emplace_back("vg170", 6.1600f);
    make_specs.emplace_back("vg180", 6.1600f);
    make_specs.emplace_back("vg190", 6.1600f);
    make_specs.emplace_back("vh210", 6.1600f);
    make_specs.emplace_back("vh410", 6.1600f);
    make_specs.emplace_back("vh510", 6.1600f);
    make_specs.emplace_back("vh515", 6.1600f);
    make_specs.emplace_back("vh520", 6.1600f);
    make_specs.emplace_back("vr310", 6.1600f);
    make_specs.emplace_back("vr320", 6.1600f);
    make_specs.emplace_back("vr330", 6.1600f);
    make_specs.emplace_back("vr340", 6.1600f);
    make_specs.emplace_back("vr350", 6.1600f);
    make_specs.emplace_back("vr360", 6.1600f);
    make_specs.emplace_back("vr370", 6.1600f);
    make_specs.emplace_back("x15", 6.0300f);
    make_specs.emplace_back("x775", 5.7500f);
    make_specs.emplace_back("x785", 5.7500f);
    make_specs.emplace_back("x905", 6.1600f);
    make_specs.emplace_back("x920", 6.1600f);
    make_specs.emplace_back("xz1", 7.8500f);
    make_specs.emplace_back("xz2ihs", 7.5300f);
  }

  {
    auto& make_specs = specs["panasonic"];
    make_specs.reserve(234);
    make_specs.emplace_back("dsnapsvas10", 4.5000f);
    make_specs.emplace_back("dsnapsvas30", 4.5000f);
    make_specs.emplace_back("dsnapsvas3", 4.5000f);
    make_specs.emplace_back("lumixdmc3d1", 6.1600f);
    make_specs.emplace_back("lumixdmcf1", 5.7500f);
    make_specs.emplace_back("lumixdmcf3", 6.0800f);
    make_specs.emplace_back("lumixdmcf5", 6.0800f);
    make_specs.emplace_back("lumixdmcf7", 5.3300f);
    make_specs.emplace_back("lumixdmcfh10", 6.0800f);
    make_specs.emplace_back("lumixdmcfh1", 6.0800f);
    make_specs.emplace_back("lumixdmcfh20", 6.0800f);
    make_specs.emplace_back("lumixdmcfh22", 6.0800f);
    make_specs.emplace_back("lumixdmcfh25", 6.1600f);
    make_specs.emplace_back("lumixdmcfh27", 6.1600f);
    make_specs.emplace_back("lumixdmcfh2", 6.0800f);
    make_specs.emplace_back("lumixdmcfh3", 6.0800f);
    make_specs.emplace_back("lumixdmcfh4", 6.0800f);
    make_specs.emplace_back("lumixdmcfh5", 6.0800f);
    make_specs.emplace_back("lumixdmcfh6", 6.0800f);
    make_specs.emplace_back("lumixdmcfh7", 6.0800f);
    make_specs.emplace_back("lumixdmcfh8", 6.0800f);
    make_specs.emplace_back("lumixdmcfp1", 6.0800f);
    make_specs.emplace_back("lumixdmcfp2", 6.0800f);
    make_specs.emplace_back("lumixdmcfp3", 6.0800f);
    make_specs.emplace_back("lumixdmcfp5", 6.0800f);
    make_specs.emplace_back("lumixdmcfp7", 6.0800f);
    make_specs.emplace_back("lumixdmcfp8", 6.0800f);
    make_specs.emplace_back("lumixdmcfs10", 6.0800f);
    make_specs.emplace_back("lumixdmcfs11", 6.0800f);
    make_specs.emplace_back("lumixdmcfs12", 6.0800f);
    make_specs.emplace_back("lumixdmcfs15", 6.0800f);
    make_specs.emplace_back("lumixdmcfs16", 6.0800f);
    make_specs.emplace_back("lumixdmcfs18", 6.0800f);
    make_specs.emplace_back("lumixdmcfs20", 6.0800f);
    make_specs.emplace_back("lumixdmcfs22", 6.0800f);
    make_specs.emplace_back("lumixdmcfs25", 6.0800f);
    make_specs.emplace_back("lumixdmcfs28", 6.0800f);
    make_specs.emplace_back("lumixdmcfs2", 5.7500f);
    make_specs.emplace_back("lumixdmcfs30", 6.0800f);
    make_specs.emplace_back("lumixdmcfs33", 6.0800f);
    make_specs.emplace_back("lumixdmcfs35", 6.1600f);
    make_specs.emplace_back("lumixdmcfs37", 6.1600f);
    make_specs.emplace_back("lumixdmcfs3", 5.7500f);
    make_specs.emplace_back("lumixdmcfs40", 6.0800f);
    make_specs.emplace_back("lumixdmcfs42", 5.7500f);
    make_specs.emplace_back("lumixdmcfs45", 6.0800f);
    make_specs.emplace_back("lumixdmcfs5", 6.0800f);
    make_specs.emplace_back("lumixdmcfs62", 5.7500f);
    make_specs.emplace_back("lumixdmcfs6", 5.7500f);
    make_specs.emplace_back("lumixdmcfs7", 5.7500f);
    make_specs.emplace_back("lumixdmcft10", 6.0800f);
    make_specs.emplace_back("lumixdmcft1", 6.0800f);
    make_specs.emplace_back("lumixdmcft20", 6.0800f);
    make_specs.emplace_back("lumixdmcft2", 6.0800f);
    make_specs.emplace_back("lumixdmcft30", 6.0800f);
    make_specs.emplace_back("lumixdmcft3", 6.0800f);
    make_specs.emplace_back("lumixdmcft4", 6.0800f);
    make_specs.emplace_back("lumixdmcfx01", 5.7500f);
    make_specs.emplace_back("lumixdmcfx07", 5.7500f);
    make_specs.emplace_back("lumixdmcfx100", 7.4400f);
    make_specs.emplace_back("lumixdmcfx10", 5.7500f);
    make_specs.emplace_back("lumixdmcfx12", 5.7500f);
    make_specs.emplace_back("lumixdmcfx150", 7.4400f);
    make_specs.emplace_back("lumixdmcfx1", 5.7500f);
    make_specs.emplace_back("lumixdmcfx2", 5.7500f);
    make_specs.emplace_back("lumixdmcfx30", 5.7500f);
    make_specs.emplace_back("lumixdmcfx33", 5.7500f);
    make_specs.emplace_back("lumixdmcfx35", 6.0800f);
    make_specs.emplace_back("lumixdmcfx37", 6.0800f);
    make_specs.emplace_back("lumixdmcfx3", 5.7500f);
    make_specs.emplace_back("lumixdmcfx40", 6.0800f);
    make_specs.emplace_back("lumixdmcfx48", 6.0800f);
    make_specs.emplace_back("lumixdmcfx500", 6.0800f);
    make_specs.emplace_back("lumixdmcfx50", 5.7500f);
    make_specs.emplace_back("lumixdmcfx550", 6.0800f);
    make_specs.emplace_back("lumixdmcfx55", 5.7500f);
    make_specs.emplace_back("lumixdmcfx580", 6.0800f);
    make_specs.emplace_back("lumixdmcfx5", 5.7500f);
    make_specs.emplace_back("lumixdmcfx60", 6.0800f);
    make_specs.emplace_back("lumixdmcfx65", 6.0800f);
    make_specs.emplace_back("lumixdmcfx66", 6.0800f);
    make_specs.emplace_back("lumixdmcfx68", 6.0800f);
    make_specs.emplace_back("lumixdmcfx700", 6.0800f);
    make_specs.emplace_back("lumixdmcfx70", 6.0800f);
    make_specs.emplace_back("lumixdmcfx75", 6.0800f);
    make_specs.emplace_back("lumixdmcfx77", 6.0800f);
    make_specs.emplace_back("lumixdmcfx78", 6.0800f);
    make_specs.emplace_back("lumixdmcfx7", 5.7500f);
    make_specs.emplace_back("lumixdmcfx80", 6.0800f);
    make_specs.emplace_back("lumixdmcfx8", 5.7500f);
    make_specs.emplace_back("lumixdmcfx90", 6.0800f);
    make_specs.emplace_back("lumixdmcfx9", 5.7500f);
    make_specs.emplace_back("lumixdmcfz1000", 13.2000f);
    make_specs.emplace_back("lumixdmcfz100", 6.0800f);
    make_specs.emplace_back("lumixdmcfz10", 5.7500f);
    make_specs.emplace_back("lumixdmcfz150", 6.0800f);
    make_specs.emplace_back("lumixdmcfz15", 5.7500f);
    make_specs.emplace_back("lumixdmcfz18", 5.7500f);
    make_specs.emplace_back("lumixdmcfz1", 4.5000f);
    make_specs.emplace_back("lumixdmcfz200", 6.1600f);
    make_specs.emplace_back("lumixdmcfz20", 5.7500f);
    make_specs.emplace_back("lumixdmcfz28", 6.0800f);
    make_specs.emplace_back("lumixdmcfz2", 4.5000f);
    make_specs.emplace_back("lumixdmcfz300", 6.1600f);
    make_specs.emplace_back("lumixdmcfz30", 7.1100f);
    make_specs.emplace_back("lumixdmcfz35", 6.0800f);
    make_specs.emplace_back("lumixdmcfz38", 6.0800f);
    make_specs.emplace_back("lumixdmcfz3", 4.5000f);
    make_specs.emplace_back("lumixdmcfz40", 6.0800f);
    make_specs.emplace_back("lumixdmcfz45", 6.0800f);
    make_specs.emplace_back("lumixdmcfz47", 6.0800f);
    make_specs.emplace_back("lumixdmcfz48", 6.0800f);
    make_specs.emplace_back("lumixdmcfz4", 5.7500f);
    make_specs.emplace_back("lumixdmcfz50", 7.1100f);
    make_specs.emplace_back("lumixdmcfz5", 5.7500f);
    make_specs.emplace_back("lumixdmcfz60", 6.0800f);
    make_specs.emplace_back("lumixdmcfz70", 6.1600f);
    make_specs.emplace_back("lumixdmcfz7", 5.7500f);
    make_specs.emplace_back("lumixdmcfz8", 5.7500f);
    make_specs.emplace_back("lumixdmcg10", 17.3000f);
    make_specs.emplace_back("lumixdmcg1", 17.3000f);
    make_specs.emplace_back("lumixdmcg2", 17.3000f);
    make_specs.emplace_back("lumixdmcg3", 17.3000f);
    make_specs.emplace_back("lumixdmcg5", 17.3000f);
    make_specs.emplace_back("lumixdmcg6", 17.3000f);
    make_specs.emplace_back("lumixdmcg7", 17.3000f);
    make_specs.emplace_back("lumixdmcgf1", 17.3000f);
    make_specs.emplace_back("lumixdmcgf2", 17.3000f);
    make_specs.emplace_back("lumixdmcgf3", 17.3000f);
    make_specs.emplace_back("lumixdmcgf5", 17.3000f);
    make_specs.emplace_back("lumixdmcgf6", 17.3000f);
    make_specs.emplace_back("lumixdmcgf7", 17.3000f);
    make_specs.emplace_back("lumixdmcgh1", 17.3000f);
    make_specs.emplace_back("lumixdmcgh2", 17.3000f);
    make_specs.emplace_back("lumixdmcgh3", 17.3000f);
    make_specs.emplace_back("lumixdmcgh4", 17.3000f);
    make_specs.emplace_back("lumixdmcgm1", 17.3000f);
    make_specs.emplace_back("lumixdmcgm5", 17.3000f);
    make_specs.emplace_back("lumixdmcgx1", 17.3000f);
    make_specs.emplace_back("lumixdmcgx7", 17.3000f);
    make_specs.emplace_back("lumixdmcgx8", 17.3000f);
    make_specs.emplace_back("lumixdmcl10", 17.3000f);
    make_specs.emplace_back("lumixdmcl1", 17.3000f);
    make_specs.emplace_back("lumixdmclc1", 8.8000f);
    make_specs.emplace_back("lumixdmclc20", 5.3300f);
    make_specs.emplace_back("lumixdmclc33", 5.7500f);
    make_specs.emplace_back("lumixdmclc40", 7.5300f);
    make_specs.emplace_back("lumixdmclc43", 5.7500f);
    make_specs.emplace_back("lumixdmclc50", 5.7500f);
    make_specs.emplace_back("lumixdmclc5", 7.5300f);
    make_specs.emplace_back("lumixdmclc70", 5.7500f);
    make_specs.emplace_back("lumixdmclc80", 5.7500f);
    make_specs.emplace_back("lumixdmclf1", 7.5300f);
    make_specs.emplace_back("lumixdmcls1", 5.7500f);
    make_specs.emplace_back("lumixdmcls2", 5.7500f);
    make_specs.emplace_back("lumixdmcls3", 5.7500f);
    make_specs.emplace_back("lumixdmcls5", 6.0800f);
    make_specs.emplace_back("lumixdmcls60", 5.7500f);
    make_specs.emplace_back("lumixdmcls6", 6.0800f);
    make_specs.emplace_back("lumixdmcls75", 5.7500f);
    make_specs.emplace_back("lumixdmcls80", 5.7500f);
    make_specs.emplace_back("lumixdmcls85", 5.7500f);
    make_specs.emplace_back("lumixdmclx100", 17.3000f);
    make_specs.emplace_back("lumixdmclx1", 7.7600f);
    make_specs.emplace_back("lumixdmclx2", 7.7600f);
    make_specs.emplace_back("lumixdmclx3", 7.8500f);
    make_specs.emplace_back("lumixdmclx5", 7.8500f);
    make_specs.emplace_back("lumixdmclx7", 7.5300f);
    make_specs.emplace_back("lumixdmclz10", 6.0800f);
    make_specs.emplace_back("lumixdmclz1", 5.7500f);
    make_specs.emplace_back("lumixdmclz20", 6.0800f);
    make_specs.emplace_back("lumixdmclz2", 5.7500f);
    make_specs.emplace_back("lumixdmclz30", 6.1600f);
    make_specs.emplace_back("lumixdmclz3", 5.7500f);
    make_specs.emplace_back("lumixdmclz40", 6.1600f);
    make_specs.emplace_back("lumixdmclz5", 5.7500f);
    make_specs.emplace_back("lumixdmclz6", 5.7500f);
    make_specs.emplace_back("lumixdmclz7", 5.7500f);
    make_specs.emplace_back("lumixdmclz8", 5.7500f);
    make_specs.emplace_back("lumixdmcs1", 6.0800f);
    make_specs.emplace_back("lumixdmcs2", 6.0800f);
    make_specs.emplace_back("lumixdmcs3", 6.0800f);
    make_specs.emplace_back("lumixdmcs5", 6.0800f);
    make_specs.emplace_back("lumixdmcsz10", 6.0800f);
    make_specs.emplace_back("lumixdmcsz1", 6.0800f);
    make_specs.emplace_back("lumixdmcsz3", 6.0800f);
    make_specs.emplace_back("lumixdmcsz5", 6.0800f);
    make_specs.emplace_back("lumixdmcsz7", 6.0800f);
    make_specs.emplace_back("lumixdmcsz8", 6.0800f);
    make_specs.emplace_back("lumixdmcsz9", 6.0800f);
    make_specs.emplace_back("lumixdmcts10", 6.0800f);
    make_specs.emplace_back("lumixdmcts1", 6.0800f);
    make_specs.emplace_back("lumixdmcts20", 6.0800f);
    make_specs.emplace_back("lumixdmcts25", 6.0800f);
    make_specs.emplace_back("lumixdmcts2", 6.0800f);
    make_specs.emplace_back("lumixdmcts3", 6.0800f);
    make_specs.emplace_back("lumixdmcts4", 6.0800f);
    make_specs.emplace_back("lumixdmcts5", 6.0800f);
    make_specs.emplace_back("lumixdmctz10", 6.0800f);
    make_specs.emplace_back("lumixdmctz18", 6.0800f);
    make_specs.emplace_back("lumixdmctz1", 5.7500f);
    make_specs.emplace_back("lumixdmctz20", 6.0800f);
    make_specs.emplace_back("lumixdmctz22", 6.0800f);
    make_specs.emplace_back("lumixdmctz25", 6.1600f);
    make_specs.emplace_back("lumixdmctz2", 6.0800f);
    make_specs.emplace_back("lumixdmctz30", 6.0800f);
    make_specs.emplace_back("lumixdmctz31", 6.0800f);
    make_specs.emplace_back("lumixdmctz3", 6.0800f);
    make_specs.emplace_back("lumixdmctz4", 5.7500f);
    make_specs.emplace_back("lumixdmctz50", 6.0800f);
    make_specs.emplace_back("lumixdmctz57", 6.0800f);
    make_specs.emplace_back("lumixdmctz5", 6.0800f);
    make_specs.emplace_back("lumixdmctz6", 5.7500f);
    make_specs.emplace_back("lumixdmctz70", 6.1600f);
    make_specs.emplace_back("lumixdmctz7", 6.0800f);
    make_specs.emplace_back("lumixdmcxs1", 6.0800f);
    make_specs.emplace_back("lumixdmcxs3", 6.0800f);
    make_specs.emplace_back("lumixdmczr1", 6.0800f);
    make_specs.emplace_back("lumixdmczr3", 6.0800f);
    make_specs.emplace_back("lumixdmczs10", 6.0800f);
    make_specs.emplace_back("lumixdmczs15", 6.1600f);
    make_specs.emplace_back("lumixdmczs1", 5.7500f);
    make_specs.emplace_back("lumixdmczs20", 6.0800f);
    make_specs.emplace_back("lumixdmczs25", 6.0800f);
    make_specs.emplace_back("lumixdmczs30", 6.1600f);
    make_specs.emplace_back("lumixdmczs35/tz55", 6.0800f);
    make_specs.emplace_back("lumixdmczs3", 6.0800f);
    make_specs.emplace_back("lumixdmczs40/tz60", 6.1600f);
    make_specs.emplace_back("lumixdmczs5", 6.0800f);
    make_specs.emplace_back("lumixdmczs7", 6.0800f);
    make_specs.emplace_back("lumixdmczs8", 6.0800f);
    make_specs.emplace_back("lumixdmczx1", 6.0800f);
    make_specs.emplace_back("lumixdmczx3", 6.0800f);
    make_specs.emplace_back("pvdc3000", 7.1100f);
  }

  {
    auto& make_specs = specs["pentax"];
    make_specs.reserve(140);
    make_specs.emplace_back("645d", 44.0000f);
    make_specs.emplace_back("645z", 44.0000f);
    make_specs.emplace_back("efina", 6.1600f);
    make_specs.emplace_back("ei100", 4.5000f);
    make_specs.emplace_back("ei2000", 8.8000f);
    make_specs.emplace_back("ei200", 5.3300f);
    make_specs.emplace_back("*istdl2", 23.5000f);
    make_specs.emplace_back("*istdl", 23.5000f);
    make_specs.emplace_back("*istds2", 23.5000f);
    make_specs.emplace_back("*istds", 23.5000f);
    make_specs.emplace_back("*istd", 23.5000f);
    make_specs.emplace_back("k01", 23.7000f);
    make_specs.emplace_back("k3ii", 23.5000f);
    make_specs.emplace_back("k30", 23.7000f);
    make_specs.emplace_back("k3", 23.5000f);
    make_specs.emplace_back("k5ii", 23.7000f);
    make_specs.emplace_back("k500", 23.7000f);
    make_specs.emplace_back("k50", 23.7000f);
    make_specs.emplace_back("k5", 23.6000f);
    make_specs.emplace_back("k7", 23.4000f);
    make_specs.emplace_back("km", 23.5000f);
    make_specs.emplace_back("kr", 23.6000f);
    make_specs.emplace_back("ks1", 23.5000f);
    make_specs.emplace_back("ks2", 23.5000f);
    make_specs.emplace_back("kx", 23.6000f);
    make_specs.emplace_back("k100dsuper", 23.5000f);
    make_specs.emplace_back("k100d", 23.5000f);
    make_specs.emplace_back("k10d", 23.5000f);
    make_specs.emplace_back("k110d", 23.7000f);
    make_specs.emplace_back("k200d", 23.5000f);
    make_specs.emplace_back("k20d", 23.4000f);
    make_specs.emplace_back("mx1", 7.5300f);
    make_specs.emplace_back("optio230", 5.3300f);
    make_specs.emplace_back("optio30", 5.3300f);
    make_specs.emplace_back("optio330gs", 5.3300f);
    make_specs.emplace_back("optio330rs", 7.1100f);
    make_specs.emplace_back("optio330", 7.1100f);
    make_specs.emplace_back("optio33lf", 5.3300f);
    make_specs.emplace_back("optio33l", 5.3300f);
    make_specs.emplace_back("optio33wr", 5.3300f);
    make_specs.emplace_back("optio430rs", 7.1100f);
    make_specs.emplace_back("optio430", 7.1100f);
    make_specs.emplace_back("optio43wr", 5.3300f);
    make_specs.emplace_back("optio450", 7.1100f);
    make_specs.emplace_back("optio50l", 5.7500f);
    make_specs.emplace_back("optio50", 5.7500f);
    make_specs.emplace_back("optio550", 7.1100f);
    make_specs.emplace_back("optio555", 7.1100f);
    make_specs.emplace_back("optio60", 7.1100f);
    make_specs.emplace_back("optio750z", 7.1100f);
    make_specs.emplace_back("optioa10", 7.1100f);
    make_specs.emplace_back("optioa20", 5.7500f);
    make_specs.emplace_back("optioa30", 7.1100f);
    make_specs.emplace_back("optioa40", 7.5300f);
    make_specs.emplace_back("optioe10", 5.7500f);
    make_specs.emplace_back("optioe20", 5.7500f);
    make_specs.emplace_back("optioe25", 5.7500f);
    make_specs.emplace_back("optioe30", 5.7500f);
    make_specs.emplace_back("optioe40", 5.7500f);
    make_specs.emplace_back("optioe50", 5.7500f);
    make_specs.emplace_back("optioe60", 6.0800f);
    make_specs.emplace_back("optioe70l", 6.1600f);
    make_specs.emplace_back("optioe70", 6.1600f);
    make_specs.emplace_back("optioe75", 6.0800f);
    make_specs.emplace_back("optioe80", 6.0800f);
    make_specs.emplace_back("optioe85", 6.1600f);
    make_specs.emplace_back("optioe90", 6.1600f);
    make_specs.emplace_back("optioh90", 6.1600f);
    make_specs.emplace_back("optioi10", 6.1600f);
    make_specs.emplace_back("optiol20", 5.7500f);
    make_specs.emplace_back("optiol50", 6.0300f);
    make_specs.emplace_back("optiols1000", 6.1600f);
    make_specs.emplace_back("optiols1100", 6.0800f);
    make_specs.emplace_back("optiols465", 23.7000f);
    make_specs.emplace_back("optiom10", 5.7500f);
    make_specs.emplace_back("optiom20", 5.7500f);
    make_specs.emplace_back("optiom30", 5.7500f);
    make_specs.emplace_back("optiom40", 6.0300f);
    make_specs.emplace_back("optiom50", 6.0300f);
    make_specs.emplace_back("optiom60", 6.1600f);
    make_specs.emplace_back("optiom85", 6.1600f);
    make_specs.emplace_back("optiom90", 6.0800f);
    make_specs.emplace_back("optiomx4", 5.3300f);
    make_specs.emplace_back("optiomx", 5.3300f);
    make_specs.emplace_back("optiop70", 6.1600f);
    make_specs.emplace_back("optiop80", 6.1600f);
    make_specs.emplace_back("optiors1000", 6.1600f);
    make_specs.emplace_back("optiors1500", 6.0800f);
    make_specs.emplace_back("optiorz10", 6.0800f);
    make_specs.emplace_back("optiorz18", 6.0800f);
    make_specs.emplace_back("optios10", 7.1100f);
    make_specs.emplace_back("optios12", 7.5300f);
    make_specs.emplace_back("optios1", 6.1600f);
    make_specs.emplace_back("optios30", 5.3300f);
    make_specs.emplace_back("optios40", 5.7500f);
    make_specs.emplace_back("optios45", 5.7500f);
    make_specs.emplace_back("optios4i", 5.7500f);
    make_specs.emplace_back("optios4", 5.7500f);
    make_specs.emplace_back("optios50", 5.7500f);
    make_specs.emplace_back("optios55", 5.7500f);
    make_specs.emplace_back("optios5i", 5.7500f);
    make_specs.emplace_back("optios5n", 5.7500f);
    make_specs.emplace_back("optios5z", 5.7500f);
    make_specs.emplace_back("optios60", 5.7500f);
    make_specs.emplace_back("optios6", 5.7500f);
    make_specs.emplace_back("optios7", 5.7500f);
    make_specs.emplace_back("optiosvi", 5.7500f);
    make_specs.emplace_back("optiosv", 5.7500f);
    make_specs.emplace_back("optios", 5.7500f);
    make_specs.emplace_back("optiot10", 5.7500f);
    make_specs.emplace_back("optiot20", 5.7500f);
    make_specs.emplace_back("optiot30", 5.7500f);
    make_specs.emplace_back("optiov10", 6.0300f);
    make_specs.emplace_back("optiov20", 6.0300f);
    make_specs.emplace_back("optiovs20", 6.0800f);
    make_specs.emplace_back("optiow10", 5.7500f);
    make_specs.emplace_back("optiow20", 5.7500f);
    make_specs.emplace_back("optiow30", 5.7500f);
    make_specs.emplace_back("optiow60", 6.1600f);
    make_specs.emplace_back("optiow80", 6.1600f);
    make_specs.emplace_back("optiow90", 6.1600f);
    make_specs.emplace_back("optiowg1gps", 6.1600f);
    make_specs.emplace_back("optiowg1", 6.1600f);
    make_specs.emplace_back("optiowg2gps", 6.1600f);
    make_specs.emplace_back("optiowg2", 6.1600f);
    make_specs.emplace_back("optiowpi", 5.7500f);
    make_specs.emplace_back("optiowp", 5.7500f);
    make_specs.emplace_back("optiows80", 6.0800f);
    make_specs.emplace_back("optiox", 5.7500f);
    make_specs.emplace_back("optioz10", 5.7500f);
    make_specs.emplace_back("qs1", 7.5300f);
    make_specs.emplace_back("q10", 6.1600f);
    make_specs.emplace_back("q7", 7.5300f);
    make_specs.emplace_back("q", 6.1600f);
    make_specs.emplace_back("wg10", 6.1600f);
    make_specs.emplace_back("wg3", 6.1600f);
    make_specs.emplace_back("x5", 6.0800f);
    make_specs.emplace_back("x70", 6.0800f);
    make_specs.emplace_back("x90", 6.0800f);
    make_specs.emplace_back("xg1", 6.0800f);
  }

  {
    auto& make_specs = specs["praktica"];
    make_specs.reserve(131);
    make_specs.emplace_back("dc20", 6.4000f);
    make_specs.emplace_back("dc21", 5.3300f);
    make_specs.emplace_back("dc22", 5.3300f);
    make_specs.emplace_back("dc32", 5.3300f);
    make_specs.emplace_back("dc34", 5.3300f);
    make_specs.emplace_back("dc42", 5.7500f);
    make_specs.emplace_back("dc44", 5.7500f);
    make_specs.emplace_back("dc50", 5.7500f);
    make_specs.emplace_back("dc52", 5.7500f);
    make_specs.emplace_back("dc60", 5.7500f);
    make_specs.emplace_back("dcslim2", 6.4000f);
    make_specs.emplace_back("dcslim5", 7.1100f);
    make_specs.emplace_back("dc440", 5.7500f);
    make_specs.emplace_back("dcz1.3", 6.4000f);
    make_specs.emplace_back("dcz10.1", 7.1100f);
    make_specs.emplace_back("dcz10.2", 6.1600f);
    make_specs.emplace_back("dcz10.3", 6.0800f);
    make_specs.emplace_back("dcz10.4", 6.1600f);
    make_specs.emplace_back("dcz12.1", 6.1600f);
    make_specs.emplace_back("dcz12.z4", 6.1600f);
    make_specs.emplace_back("dcz14.1", 6.1600f);
    make_specs.emplace_back("dcz14.2", 6.1600f);
    make_specs.emplace_back("dcz2.0", 4.8000f);
    make_specs.emplace_back("dcz2.1s", 4.5000f);
    make_specs.emplace_back("dcz2.1", 4.8000f);
    make_specs.emplace_back("dcz2.2s", 6.4000f);
    make_specs.emplace_back("dcz2.2", 5.3300f);
    make_specs.emplace_back("dcz3.0", 6.4000f);
    make_specs.emplace_back("dcz3.2d", 6.4000f);
    make_specs.emplace_back("dcz3.2s", 6.4000f);
    make_specs.emplace_back("dcz3.2", 7.1100f);
    make_specs.emplace_back("dcz3.3", 7.1100f);
    make_specs.emplace_back("dcz3.4", 5.7500f);
    make_specs.emplace_back("dcz3.5", 5.7500f);
    make_specs.emplace_back("dcz4.1", 7.1100f);
    make_specs.emplace_back("dcz4.2", 7.1100f);
    make_specs.emplace_back("dcz4.3", 7.1100f);
    make_specs.emplace_back("dcz4.4", 5.7500f);
    make_specs.emplace_back("dcz5.1", 7.1100f);
    make_specs.emplace_back("dcz5.2", 7.1100f);
    make_specs.emplace_back("dcz5.3", 5.7500f);
    make_specs.emplace_back("dcz5.4", 5.7500f);
    make_specs.emplace_back("dcz5.5", 5.7500f);
    make_specs.emplace_back("dcz5.8", 5.7500f);
    make_specs.emplace_back("dcz6.1", 5.7500f);
    make_specs.emplace_back("dcz6.2", 5.7500f);
    make_specs.emplace_back("dcz6.3", 5.7500f);
    make_specs.emplace_back("dcz6.8", 5.7500f);
    make_specs.emplace_back("dcz7.1", 5.7500f);
    make_specs.emplace_back("dcz7.2", 5.7500f);
    make_specs.emplace_back("dcz7.3", 5.7500f);
    make_specs.emplace_back("dcz7.4", 5.7500f);
    make_specs.emplace_back("dcz8.1", 5.7500f);
    make_specs.emplace_back("dcz8.2", 5.7500f);
    make_specs.emplace_back("dcz8.3", 5.7500f);
    make_specs.emplace_back("digi3lm", 6.4000f);
    make_specs.emplace_back("digi30", 6.4000f);
    make_specs.emplace_back("digi3", 6.4000f);
    make_specs.emplace_back("digicam3", 6.4000f);
    make_specs.emplace_back("dmmc4", 4.8000f);
    make_specs.emplace_back("dmmc", 4.8000f);
    make_specs.emplace_back("dpix1000z", 6.1600f);
    make_specs.emplace_back("dpix1100z", 5.7500f);
    make_specs.emplace_back("dpix1220z", 6.0800f);
    make_specs.emplace_back("dpix3000", 6.4000f);
    make_specs.emplace_back("dpix3200", 4.8000f);
    make_specs.emplace_back("dpix3300", 4.8000f);
    make_specs.emplace_back("dpix5000wp", 4.5000f);
    make_specs.emplace_back("dpix5100", 5.7500f);
    make_specs.emplace_back("dpix510z", 5.7500f);
    make_specs.emplace_back("dpix5200", 5.7500f);
    make_specs.emplace_back("dpix530z", 5.7500f);
    make_specs.emplace_back("dpix740z", 5.7500f);
    make_specs.emplace_back("dpix750z", 5.7500f);
    make_specs.emplace_back("dpix810z", 5.7500f);
    make_specs.emplace_back("dpix820z", 5.7500f);
    make_specs.emplace_back("dpix9000", 6.1600f);
    make_specs.emplace_back("dpix910z", 6.4000f);
    make_specs.emplace_back("dvc6.1", 5.7500f);
    make_specs.emplace_back("exaktadc4200", 7.1100f);
    make_specs.emplace_back("g2.0", 6.4000f);
    make_specs.emplace_back("g3.2", 6.4000f);
    make_specs.emplace_back("luxmedia1003", 6.1600f);
    make_specs.emplace_back("luxmedia1023", 6.1600f);
    make_specs.emplace_back("luxmedia10ts", 6.1600f);
    make_specs.emplace_back("luxmedia10x3", 7.1100f);
    make_specs.emplace_back("luxmedia10xs", 5.7500f);
    make_specs.emplace_back("luxmedia1203", 5.7500f);
    make_specs.emplace_back("luxmedia1204", 6.1600f);
    make_specs.emplace_back("luxmedia1223", 6.1600f);
    make_specs.emplace_back("luxmedia12hd", 7.4400f);
    make_specs.emplace_back("luxmedia12ts", 6.1600f);
    make_specs.emplace_back("luxmedia12xs", 5.7500f);
    make_specs.emplace_back("luxmedia12z4ts", 6.1600f);
    make_specs.emplace_back("luxmedia12z4", 6.1600f);
    make_specs.emplace_back("luxmedia12z5", 6.1600f);
    make_specs.emplace_back("luxmedia14z50s", 6.1600f);
    make_specs.emplace_back("luxmedia14z51", 6.1600f);
    make_specs.emplace_back("luxmedia14z80s", 6.1600f);
    make_specs.emplace_back("luxmedia16z12s", 6.1600f);
    make_specs.emplace_back("luxmedia16z21c", 6.1600f);
    make_specs.emplace_back("luxmedia16z21s", 6.1600f);
    make_specs.emplace_back("luxmedia16z24s", 6.1600f);
    make_specs.emplace_back("luxmedia16z51", 6.1600f);
    make_specs.emplace_back("luxmedia16z52", 6.1600f);
    make_specs.emplace_back("luxmedia18z36c", 6.1600f);
    make_specs.emplace_back("luxmedia4008", 5.7500f);
    make_specs.emplace_back("luxmedia5003", 7.1100f);
    make_specs.emplace_back("luxmedia5008", 5.7500f);
    make_specs.emplace_back("luxmedia5103", 7.1100f);
    make_specs.emplace_back("luxmedia5203", 5.7500f);
    make_specs.emplace_back("luxmedia5303", 5.7500f);
    make_specs.emplace_back("luxmedia6103", 7.1100f);
    make_specs.emplace_back("luxmedia6105", 5.7500f);
    make_specs.emplace_back("luxmedia6203", 5.7500f);
    make_specs.emplace_back("luxmedia6403", 5.7500f);
    make_specs.emplace_back("luxmedia6503", 5.7500f);
    make_specs.emplace_back("luxmedia7103", 5.7500f);
    make_specs.emplace_back("luxmedia7105", 5.7500f);
    make_specs.emplace_back("luxmedia7203", 5.7500f);
    make_specs.emplace_back("luxmedia7303", 5.7500f);
    make_specs.emplace_back("luxmedia7403", 5.7500f);
    make_specs.emplace_back("luxmedia8003", 7.1100f);
    make_specs.emplace_back("luxmedia8203", 5.7500f);
    make_specs.emplace_back("luxmedia8213", 5.7500f);
    make_specs.emplace_back("luxmedia8303", 5.7500f);
    make_specs.emplace_back("luxmedia8403", 5.7500f);
    make_specs.emplace_back("luxmedia8503", 5.7500f);
    make_specs.emplace_back("mini", 6.4000f);
    make_specs.emplace_back("v2.1", 6.4000f);
    make_specs.emplace_back("v3.2", 6.4000f);
  }

  {
    auto& make_specs = specs["ricoh"];
    make_specs.reserve(76);
    make_specs.emplace_back("caplio400gwide", 5.3300f);
    make_specs.emplace_back("caplio500gwide", 7.1100f);
    make_specs.emplace_back("caplio500g", 7.1100f);
    make_specs.emplace_back("caplio500se", 7.1100f);
    make_specs.emplace_back("capliog3s", 5.3300f);
    make_specs.emplace_back("capliog3", 5.3300f);
    make_specs.emplace_back("capliogx100", 7.3100f);
    make_specs.emplace_back("capliogx200", 7.5300f);
    make_specs.emplace_back("capliogx8", 7.1100f);
    make_specs.emplace_back("capliogx", 7.1100f);
    make_specs.emplace_back("caplior1v", 5.7500f);
    make_specs.emplace_back("caplior1", 7.1100f);
    make_specs.emplace_back("caplior2s", 5.7500f);
    make_specs.emplace_back("caplior2", 5.7500f);
    make_specs.emplace_back("caplior30", 5.7500f);
    make_specs.emplace_back("caplior3", 5.7500f);
    make_specs.emplace_back("caplior40", 5.7500f);
    make_specs.emplace_back("caplior4", 5.7500f);
    make_specs.emplace_back("caplior5", 5.7500f);
    make_specs.emplace_back("caplior6", 5.7500f);
    make_specs.emplace_back("caplior7", 5.7500f);
    make_specs.emplace_back("caplior8", 6.1600f);
    make_specs.emplace_back("capliorr10", 5.3300f);
    make_specs.emplace_back("capliorr120", 4.5000f);
    make_specs.emplace_back("capliorr1", 7.1100f);
    make_specs.emplace_back("capliorr230", 4.5000f);
    make_specs.emplace_back("capliorr30", 5.3300f);
    make_specs.emplace_back("capliorr330", 5.7500f);
    make_specs.emplace_back("capliorr530", 5.7500f);
    make_specs.emplace_back("capliorr630", 7.1100f);
    make_specs.emplace_back("capliorr660", 5.7500f);
    make_specs.emplace_back("capliorr750", 5.7500f);
    make_specs.emplace_back("capliorr770", 5.7500f);
    make_specs.emplace_back("capliorx", 5.3300f);
    make_specs.emplace_back("capliorz1", 5.7500f);
    make_specs.emplace_back("cx1", 6.1600f);
    make_specs.emplace_back("cx2", 6.1600f);
    make_specs.emplace_back("cx3", 6.1600f);
    make_specs.emplace_back("cx4", 6.1600f);
    make_specs.emplace_back("cx5", 6.1600f);
    make_specs.emplace_back("cx6", 6.1600f);
    make_specs.emplace_back("g600", 6.1600f);
    make_specs.emplace_back("g700se", 6.1600f);
    make_specs.emplace_back("g700", 6.1600f);
    make_specs.emplace_back("g800", 6.1600f);
    make_specs.emplace_back("grdigital3", 7.5300f);
    make_specs.emplace_back("grdigital4", 7.5300f);
    make_specs.emplace_back("grdigitalii", 7.3100f);
    make_specs.emplace_back("grdigital", 7.1100f);
    make_specs.emplace_back("grii", 23.6000f);
    make_specs.emplace_back("gr", 23.6000f);
    make_specs.emplace_back("gx200", 7.5300f);
    make_specs.emplace_back("gxra1250mmf2.5macro", 23.6000f);
    make_specs.emplace_back("gxra162485mmf3.55.5", 23.6000f);
    make_specs.emplace_back("gxrgrlensa1228mmf2.5", 23.6000f);
    make_specs.emplace_back("gxrmounta12", 23.6000f);
    make_specs.emplace_back("gxrp1028300mmf3.55.6vc", 6.1600f);
    make_specs.emplace_back("gxrs102472mmf2.54.4vc", 7.5300f);
    make_specs.emplace_back("hz15", 6.1600f);
    make_specs.emplace_back("px", 6.1600f);
    make_specs.emplace_back("r10", 6.1600f);
    make_specs.emplace_back("r50", 6.0800f);
    make_specs.emplace_back("r8", 6.1600f);
    make_specs.emplace_back("rdc200g", 6.4000f);
    make_specs.emplace_back("rdc4300", 4.8000f);
    make_specs.emplace_back("rdc5000", 5.3300f);
    make_specs.emplace_back("rdc5300", 5.3300f);
    make_specs.emplace_back("rdc6000", 6.4000f);
    make_specs.emplace_back("rdc7", 7.1100f);
    make_specs.emplace_back("rdci500", 7.1100f);
    make_specs.emplace_back("rdci700", 7.1100f);
    make_specs.emplace_back("wg20", 6.1600f);
    make_specs.emplace_back("wg30", 6.1600f);
    make_specs.emplace_back("wg4", 6.1600f);
    make_specs.emplace_back("wg5gps", 6.1600f);
    make_specs.emplace_back("wgm1", 6.1600f);
  }

  {
    auto& make_specs = specs["rollei"];
    make_specs.reserve(121);
    make_specs.emplace_back("compactline100", 6.1600f);
    make_specs.emplace_back("compactline101", 5.7500f);
    make_specs.emplace_back("compactline102", 5.7500f);
    make_specs.emplace_back("compactline103", 6.0800f);
    make_specs.emplace_back("compactline110", 5.7500f);
    make_specs.emplace_back("compactline130", 5.7500f);
    make_specs.emplace_back("compactline150", 6.0800f);
    make_specs.emplace_back("compactline200", 6.1600f);
    make_specs.emplace_back("compactline202", 6.0800f);
    make_specs.emplace_back("compactline203", 6.0800f);
    make_specs.emplace_back("compactline230", 6.0800f);
    make_specs.emplace_back("compactline302", 6.0800f);
    make_specs.emplace_back("compactline304", 6.0800f);
    make_specs.emplace_back("compactline312", 6.1600f);
    make_specs.emplace_back("compactline320", 6.0800f);
    make_specs.emplace_back("compactline350", 6.1600f);
    make_specs.emplace_back("compactline360ts", 6.1600f);
    make_specs.emplace_back("compactline370ts", 6.1600f);
    make_specs.emplace_back("compactline390se", 6.1600f);
    make_specs.emplace_back("compactline412", 6.0800f);
    make_specs.emplace_back("compactline415", 6.0800f);
    make_specs.emplace_back("compactline425", 6.1600f);
    make_specs.emplace_back("compactline50", 6.1600f);
    make_specs.emplace_back("compactline52", 6.1600f);
    make_specs.emplace_back("compactline55", 6.1600f);
    make_specs.emplace_back("compactline80", 5.7500f);
    make_specs.emplace_back("compactline81", 5.7500f);
    make_specs.emplace_back("compactline90", 6.1600f);
    make_specs.emplace_back("d20motion", 7.1100f);
    make_specs.emplace_back("d210motion", 4.5000f);
    make_specs.emplace_back("d23com", 7.5300f);
    make_specs.emplace_back("d33com", 7.1100f);
    make_specs.emplace_back("d330motion", 5.3300f);
    make_specs.emplace_back("d41com", 7.1100f);
    make_specs.emplace_back("d530flex", 8.8000f);
    make_specs.emplace_back("da10", 5.7500f);
    make_specs.emplace_back("da1325prego", 5.7500f);
    make_specs.emplace_back("da5324", 5.7500f);
    make_specs.emplace_back("da5325prego", 5.7500f);
    make_specs.emplace_back("da6324", 5.7500f);
    make_specs.emplace_back("da7325prego", 5.7500f);
    make_specs.emplace_back("da8324", 5.7500f);
    make_specs.emplace_back("dc3100", 5.3300f);
    make_specs.emplace_back("dcx310", 7.1100f);
    make_specs.emplace_back("dcx400", 7.1100f);
    make_specs.emplace_back("dk3000", 5.3300f);
    make_specs.emplace_back("dk4010", 5.3300f);
    make_specs.emplace_back("dp300", 5.3300f);
    make_specs.emplace_back("dp3210", 5.3300f);
    make_specs.emplace_back("dp6500", 7.1100f);
    make_specs.emplace_back("dpx310", 5.3300f);
    make_specs.emplace_back("dr5100", 7.1100f);
    make_specs.emplace_back("dr5", 5.7500f);
    make_specs.emplace_back("ds6", 5.7500f);
    make_specs.emplace_back("dsx410", 5.7500f);
    make_specs.emplace_back("dt3200", 5.7500f);
    make_specs.emplace_back("dt4000", 5.7500f);
    make_specs.emplace_back("dt4200", 5.7500f);
    make_specs.emplace_back("dt6tribute", 5.7500f);
    make_specs.emplace_back("dx63", 5.7500f);
    make_specs.emplace_back("flexline100it", 5.7500f);
    make_specs.emplace_back("flexline100", 5.7500f);
    make_specs.emplace_back("flexline140", 6.0800f);
    make_specs.emplace_back("flexline200", 6.0800f);
    make_specs.emplace_back("flexline202", 6.1600f);
    make_specs.emplace_back("flexline250", 6.0800f);
    make_specs.emplace_back("kids100", 8.8000f);
    make_specs.emplace_back("powerflex240hd", 6.1600f);
    make_specs.emplace_back("powerflex360fullhd", 6.1600f);
    make_specs.emplace_back("powerflex3d", 6.0800f);
    make_specs.emplace_back("powerflex400", 6.0800f);
    make_specs.emplace_back("powerflex440", 6.0800f);
    make_specs.emplace_back("powerflex450", 6.0800f);
    make_specs.emplace_back("powerflex455", 6.0800f);
    make_specs.emplace_back("powerflex460", 6.0800f);
    make_specs.emplace_back("powerflex470", 6.0800f);
    make_specs.emplace_back("powerflex500", 6.0800f);
    make_specs.emplace_back("powerflex600", 6.1600f);
    make_specs.emplace_back("powerflex610hd", 6.1600f);
    make_specs.emplace_back("powerflex700fullhd", 6.0800f);
    make_specs.emplace_back("powerflex800", 6.0800f);
    make_specs.emplace_back("powerflex820", 6.0800f);
    make_specs.emplace_back("pregoda3", 5.3300f);
    make_specs.emplace_back("pregoda4", 5.7500f);
    make_specs.emplace_back("pregoda5", 5.7500f);
    make_specs.emplace_back("pregoda6", 7.1100f);
    make_specs.emplace_back("pregodp4200", 5.7500f);
    make_specs.emplace_back("pregodp5200", 5.7500f);
    make_specs.emplace_back("pregodp5300", 7.1100f);
    make_specs.emplace_back("pregodp5500", 5.7500f);
    make_specs.emplace_back("pregodp6000", 7.1100f);
    make_specs.emplace_back("pregodp6200", 7.1100f);
    make_specs.emplace_back("pregodp6300", 7.1100f);
    make_specs.emplace_back("pregodp8300", 7.1100f);
    make_specs.emplace_back("rcp10325x", 7.1100f);
    make_specs.emplace_back("rcp5324", 5.7500f);
    make_specs.emplace_back("rcp6324", 5.7500f);
    make_specs.emplace_back("rcp7324", 5.7500f);
    make_specs.emplace_back("rcp7325xs", 5.7500f);
    make_specs.emplace_back("rcp7330x", 5.7500f);
    make_specs.emplace_back("rcp7430xw", 5.7500f);
    make_specs.emplace_back("rcp8325xs", 5.7500f);
    make_specs.emplace_back("rcp8325x", 7.1100f);
    make_specs.emplace_back("rcp8325", 5.7500f);
    make_specs.emplace_back("rcp8330x", 5.7500f);
    make_specs.emplace_back("rcp8427xw", 5.7500f);
    make_specs.emplace_back("rcp8527x", 5.7500f);
    make_specs.emplace_back("rcps10", 6.1600f);
    make_specs.emplace_back("rcps8", 5.7500f);
    make_specs.emplace_back("sportsline50", 6.1600f);
    make_specs.emplace_back("sportsline60camouflage", 6.1600f);
    make_specs.emplace_back("sportsline62", 6.1600f);
    make_specs.emplace_back("sportsline90", 6.1600f);
    make_specs.emplace_back("sportsline99", 6.1600f);
    make_specs.emplace_back("x8compact", 5.7500f);
    make_specs.emplace_back("x8sports", 5.7500f);
    make_specs.emplace_back("x8", 5.7500f);
    make_specs.emplace_back("xs10intouch", 6.1600f);
    make_specs.emplace_back("xs10", 5.7500f);
    make_specs.emplace_back("xs8crystal", 5.7500f);
    make_specs.emplace_back("xs8", 5.7500f);
  }

  {
    auto& make_specs = specs["samsung"];
    make_specs.reserve(279);
    make_specs.emplace_back("aq100", 6.0800f);
    make_specs.emplace_back("cl5", 5.7500f);
    make_specs.emplace_back("cl65", 6.0800f);
    make_specs.emplace_back("cl80", 6.1600f);
    make_specs.emplace_back("d75", 5.7500f);
    make_specs.emplace_back("d830", 7.1100f);
    make_specs.emplace_back("d85", 5.7500f);
    make_specs.emplace_back("d860", 5.7500f);
    make_specs.emplace_back("digimax101", 6.4000f);
    make_specs.emplace_back("digimax130", 4.5000f);
    make_specs.emplace_back("digimax200", 5.3300f);
    make_specs.emplace_back("digimax201", 4.5000f);
    make_specs.emplace_back("digimax202", 6.4000f);
    make_specs.emplace_back("digimax210se", 5.3300f);
    make_specs.emplace_back("digimax220se", 5.3300f);
    make_specs.emplace_back("digimax230", 5.3300f);
    make_specs.emplace_back("digimax240", 4.5000f);
    make_specs.emplace_back("digimax250", 4.5000f);
    make_specs.emplace_back("digimax301", 5.3300f);
    make_specs.emplace_back("digimax330", 7.5300f);
    make_specs.emplace_back("digimax340", 7.1100f);
    make_specs.emplace_back("digimax35mp3", 4.8000f);
    make_specs.emplace_back("digimax350se", 7.1100f);
    make_specs.emplace_back("digimax360", 7.1100f);
    make_specs.emplace_back("digimax370", 5.7500f);
    make_specs.emplace_back("digimax401", 5.7500f);
    make_specs.emplace_back("digimax410", 7.1100f);
    make_specs.emplace_back("digimax420", 7.1100f);
    make_specs.emplace_back("digimax430", 5.7500f);
    make_specs.emplace_back("digimax50duo", 4.8000f);
    make_specs.emplace_back("digimax530", 7.1100f);
    make_specs.emplace_back("digimaxa400", 5.7500f);
    make_specs.emplace_back("digimaxa402", 5.7500f);
    make_specs.emplace_back("digimaxa502", 5.7500f);
    make_specs.emplace_back("digimaxa503", 5.7500f);
    make_specs.emplace_back("digimaxa50", 5.7500f);
    make_specs.emplace_back("digimaxa55w", 5.7500f);
    make_specs.emplace_back("digimaxa5", 7.1100f);
    make_specs.emplace_back("digimaxa6", 7.1100f);
    make_specs.emplace_back("digimaxa7", 7.1100f);
    make_specs.emplace_back("digimaxd103", 7.1100f);
    make_specs.emplace_back("digimaxi50mp3", 5.7500f);
    make_specs.emplace_back("digimaxi5", 5.7500f);
    make_specs.emplace_back("digimaxi6", 5.7500f);
    make_specs.emplace_back("digimaxl50", 5.7500f);
    make_specs.emplace_back("digimaxl55w", 5.7500f);
    make_specs.emplace_back("digimaxl60", 5.7500f);
    make_specs.emplace_back("digimaxl70", 5.7500f);
    make_specs.emplace_back("digimaxl85", 7.1100f);
    make_specs.emplace_back("digimaxs1000", 7.1100f);
    make_specs.emplace_back("digimaxs500", 5.7500f);
    make_specs.emplace_back("digimaxs600", 5.7500f);
    make_specs.emplace_back("digimaxs700", 5.7500f);
    make_specs.emplace_back("digimaxs800", 7.1100f);
    make_specs.emplace_back("digimaxuca3", 5.3300f);
    make_specs.emplace_back("digimaxuca401", 5.7500f);
    make_specs.emplace_back("digimaxuca4", 5.7500f);
    make_specs.emplace_back("digimaxuca501", 5.7500f);
    make_specs.emplace_back("digimaxuca505", 5.7500f);
    make_specs.emplace_back("digimaxuca5", 5.7500f);
    make_specs.emplace_back("digimaxv3", 7.1100f);
    make_specs.emplace_back("digimaxv4000", 7.1100f);
    make_specs.emplace_back("digimaxv40", 7.1100f);
    make_specs.emplace_back("digimaxv4", 7.1100f);
    make_specs.emplace_back("digimaxv50", 7.1100f);
    make_specs.emplace_back("digimaxv5", 7.1100f);
    make_specs.emplace_back("digimaxv600", 7.1100f);
    make_specs.emplace_back("digimaxv6", 6.7400f);
    make_specs.emplace_back("digimaxv700", 7.1100f);
    make_specs.emplace_back("digimaxv70", 7.1100f);
    make_specs.emplace_back("digimaxv800", 7.1100f);
    make_specs.emplace_back("dv100", 6.1600f);
    make_specs.emplace_back("dv150f", 6.1600f);
    make_specs.emplace_back("dv300f", 6.1600f);
    make_specs.emplace_back("es10", 5.7500f);
    make_specs.emplace_back("es15", 6.0800f);
    make_specs.emplace_back("es17", 6.0800f);
    make_specs.emplace_back("es20", 6.0800f);
    make_specs.emplace_back("es25", 6.0800f);
    make_specs.emplace_back("es28", 6.0800f);
    make_specs.emplace_back("es30", 6.1600f);
    make_specs.emplace_back("es50", 6.0800f);
    make_specs.emplace_back("es55", 6.0800f);
    make_specs.emplace_back("es60", 6.0800f);
    make_specs.emplace_back("es65", 6.0800f);
    make_specs.emplace_back("es70", 6.0800f);
    make_specs.emplace_back("es73", 6.0800f);
    make_specs.emplace_back("es75", 6.0800f);
    make_specs.emplace_back("es80", 6.1600f);
    make_specs.emplace_back("es90", 6.1600f);
    make_specs.emplace_back("es95", 6.1600f);
    make_specs.emplace_back("ex1", 7.5300f);
    make_specs.emplace_back("ex2f", 7.5300f);
    make_specs.emplace_back("galaxycamera2", 6.1600f);
    make_specs.emplace_back("galaxycamera", 6.1600f);
    make_specs.emplace_back("galaxynx", 23.5000f);
    make_specs.emplace_back("gx10", 23.5000f);
    make_specs.emplace_back("gx1l", 23.5000f);
    make_specs.emplace_back("gx1s", 23.5000f);
    make_specs.emplace_back("gx20", 23.4000f);
    make_specs.emplace_back("hz10w", 6.0800f);
    make_specs.emplace_back("hz15w", 6.0800f);
    make_specs.emplace_back("hz25w", 6.0800f);
    make_specs.emplace_back("hz30w", 6.1600f);
    make_specs.emplace_back("hz35w", 6.1600f);
    make_specs.emplace_back("hz50w", 6.1600f);
    make_specs.emplace_back("i100", 6.0800f);
    make_specs.emplace_back("i70", 5.7500f);
    make_specs.emplace_back("i7", 5.7500f);
    make_specs.emplace_back("i80", 5.7500f);
    make_specs.emplace_back("i85", 5.7500f);
    make_specs.emplace_back("i8", 5.7500f);
    make_specs.emplace_back("it100", 6.0800f);
    make_specs.emplace_back("l100", 5.7500f);
    make_specs.emplace_back("l110", 5.7500f);
    make_specs.emplace_back("l200", 5.7500f);
    make_specs.emplace_back("l201", 6.0800f);
    make_specs.emplace_back("l210", 6.0800f);
    make_specs.emplace_back("l301", 6.0800f);
    make_specs.emplace_back("l310w", 7.4400f);
    make_specs.emplace_back("l700", 5.7500f);
    make_specs.emplace_back("l730", 5.7500f);
    make_specs.emplace_back("l73", 5.7500f);
    make_specs.emplace_back("l74wide", 5.7500f);
    make_specs.emplace_back("l74", 5.7500f);
    make_specs.emplace_back("l77", 5.7500f);
    make_specs.emplace_back("l80", 7.1100f);
    make_specs.emplace_back("l830", 5.7500f);
    make_specs.emplace_back("l83t", 5.7500f);
    make_specs.emplace_back("m100", 5.7500f);
    make_specs.emplace_back("miniketvpms10", 5.7500f);
    make_specs.emplace_back("miniketvpms11", 5.7500f);
    make_specs.emplace_back("miniketvpms15", 5.7500f);
    make_specs.emplace_back("mv800", 6.1600f);
    make_specs.emplace_back("nv100hd", 7.4400f);
    make_specs.emplace_back("nv10", 7.1100f);
    make_specs.emplace_back("nv11", 7.1100f);
    make_specs.emplace_back("nv15", 7.2700f);
    make_specs.emplace_back("nv20", 7.4400f);
    make_specs.emplace_back("nv24hd", 6.1600f);
    make_specs.emplace_back("nv30", 5.7500f);
    make_specs.emplace_back("nv3", 5.7500f);
    make_specs.emplace_back("nv40", 6.0800f);
    make_specs.emplace_back("nv4", 5.7500f);
    make_specs.emplace_back("nv7ops", 5.7500f);
    make_specs.emplace_back("nv8", 7.2700f);
    make_specs.emplace_back("nv9", 6.1600f);
    make_specs.emplace_back("nxmini", 13.2000f);
    make_specs.emplace_back("nx1000", 23.5000f);
    make_specs.emplace_back("nx100", 23.4000f);
    make_specs.emplace_back("nx10", 23.4000f);
    make_specs.emplace_back("nx1100", 23.5000f);
    make_specs.emplace_back("nx11", 23.4000f);
    make_specs.emplace_back("nx1", 23.5000f);
    make_specs.emplace_back("nx2000", 23.5000f);
    make_specs.emplace_back("nx200", 23.5000f);
    make_specs.emplace_back("nx20", 23.5000f);
    make_specs.emplace_back("nx210", 23.5000f);
    make_specs.emplace_back("nx3000", 23.5000f);
    make_specs.emplace_back("nx300", 23.5000f);
    make_specs.emplace_back("nx30", 23.5000f);
    make_specs.emplace_back("nx500", 23.5000f);
    make_specs.emplace_back("nx5", 23.4000f);
    make_specs.emplace_back("pl100", 6.0800f);
    make_specs.emplace_back("pl10", 5.7500f);
    make_specs.emplace_back("pl120", 6.1600f);
    make_specs.emplace_back("pl150", 6.0800f);
    make_specs.emplace_back("pl160", 6.0800f);
    make_specs.emplace_back("pl170", 6.0800f);
    make_specs.emplace_back("pl200", 6.1600f);
    make_specs.emplace_back("pl20", 6.1600f);
    make_specs.emplace_back("pl210", 6.1600f);
    make_specs.emplace_back("pl50", 6.0800f);
    make_specs.emplace_back("pl51", 6.1600f);
    make_specs.emplace_back("pl55", 6.0800f);
    make_specs.emplace_back("pl60", 6.0800f);
    make_specs.emplace_back("pl65", 6.0800f);
    make_specs.emplace_back("pl70", 6.0800f);
    make_specs.emplace_back("pl80", 6.0800f);
    make_specs.emplace_back("pl90", 6.0800f);
    make_specs.emplace_back("pro815", 8.8000f);
    make_specs.emplace_back("s1030", 7.1100f);
    make_specs.emplace_back("s1050", 7.1100f);
    make_specs.emplace_back("s1060", 6.0800f);
    make_specs.emplace_back("s1070", 6.0800f);
    make_specs.emplace_back("s630", 5.7500f);
    make_specs.emplace_back("s730", 5.7500f);
    make_specs.emplace_back("s750", 5.7500f);
    make_specs.emplace_back("s760", 5.7500f);
    make_specs.emplace_back("s830", 7.1100f);
    make_specs.emplace_back("s850", 7.1100f);
    make_specs.emplace_back("s85", 5.7500f);
    make_specs.emplace_back("s860", 5.7500f);
    make_specs.emplace_back("sdcms61", 5.7500f);
    make_specs.emplace_back("sh100", 6.0800f);
    make_specs.emplace_back("sl102", 6.0800f);
    make_specs.emplace_back("sl201", 6.0800f);
    make_specs.emplace_back("sl202", 6.0800f);
    make_specs.emplace_back("sl30", 6.0800f);
    make_specs.emplace_back("sl310w", 7.4400f);
    make_specs.emplace_back("sl502", 6.0800f);
    make_specs.emplace_back("sl50", 6.0800f);
    make_specs.emplace_back("sl600", 6.0800f);
    make_specs.emplace_back("sl605", 6.0800f);
    make_specs.emplace_back("sl620", 6.0800f);
    make_specs.emplace_back("sl630", 6.0800f);
    make_specs.emplace_back("sl720", 6.0800f);
    make_specs.emplace_back("sl820", 6.0800f);
    make_specs.emplace_back("st1000", 6.1600f);
    make_specs.emplace_back("st100", 6.1600f);
    make_specs.emplace_back("st10", 5.7500f);
    make_specs.emplace_back("st150f", 6.1600f);
    make_specs.emplace_back("st200f", 6.1600f);
    make_specs.emplace_back("st30", 4.8000f);
    make_specs.emplace_back("st45", 6.0800f);
    make_specs.emplace_back("st5000", 6.1600f);
    make_specs.emplace_back("st500", 6.1600f);
    make_specs.emplace_back("st50", 6.0800f);
    make_specs.emplace_back("st5500", 6.1600f);
    make_specs.emplace_back("st550", 6.0800f);
    make_specs.emplace_back("st600", 6.0800f);
    make_specs.emplace_back("st60", 6.1600f);
    make_specs.emplace_back("st6500", 6.0800f);
    make_specs.emplace_back("st65", 6.1600f);
    make_specs.emplace_back("st66", 6.1600f);
    make_specs.emplace_back("st700", 6.1600f);
    make_specs.emplace_back("st70", 6.1600f);
    make_specs.emplace_back("st72", 6.1600f);
    make_specs.emplace_back("st76", 6.1600f);
    make_specs.emplace_back("st77", 6.1600f);
    make_specs.emplace_back("st80", 6.0800f);
    make_specs.emplace_back("st88", 6.1600f);
    make_specs.emplace_back("st90", 6.1600f);
    make_specs.emplace_back("st93", 6.1600f);
    make_specs.emplace_back("st95", 6.1600f);
    make_specs.emplace_back("st96", 6.1600f);
    make_specs.emplace_back("tl100", 6.0800f);
    make_specs.emplace_back("tl105", 6.1600f);
    make_specs.emplace_back("tl110", 6.1600f);
    make_specs.emplace_back("tl205", 6.0800f);
    make_specs.emplace_back("tl210", 6.0800f);
    make_specs.emplace_back("tl220", 6.0800f);
    make_specs.emplace_back("tl225", 6.0800f);
    make_specs.emplace_back("tl240", 6.1600f);
    make_specs.emplace_back("tl320", 6.0800f);
    make_specs.emplace_back("tl34hd", 7.4400f);
    make_specs.emplace_back("tl350", 6.0800f);
    make_specs.emplace_back("tl500", 7.5300f);
    make_specs.emplace_back("tl9", 6.1600f);
    make_specs.emplace_back("wb1000", 6.0800f);
    make_specs.emplace_back("wb100", 6.1600f);
    make_specs.emplace_back("wb1100f", 6.1600f);
    make_specs.emplace_back("wb110", 6.1600f);
    make_specs.emplace_back("wb150f", 6.1600f);
    make_specs.emplace_back("wb2000", 6.0800f);
    make_specs.emplace_back("wb200f", 6.1600f);
    make_specs.emplace_back("wb2100", 6.1600f);
    make_specs.emplace_back("wb210", 6.1600f);
    make_specs.emplace_back("wb2200f", 6.1600f);
    make_specs.emplace_back("wb250f", 6.1600f);
    make_specs.emplace_back("wb30f", 6.1600f);
    make_specs.emplace_back("wb350f", 6.1600f);
    make_specs.emplace_back("wb35f", 6.1600f);
    make_specs.emplace_back("wb5000", 6.0800f);
    make_specs.emplace_back("wb500", 6.0800f);
    make_specs.emplace_back("wb50f", 6.1600f);
    make_specs.emplace_back("wb510", 6.0800f);
    make_specs.emplace_back("wb5500", 6.1600f);
    make_specs.emplace_back("wb550", 6.0800f);
    make_specs.emplace_back("wb560", 6.0800f);
    make_specs.emplace_back("wb600", 6.1600f);
    make_specs.emplace_back("wb650", 6.1600f);
    make_specs.emplace_back("wb660", 6.1600f);
    make_specs.emplace_back("wb690", 6.0800f);
    make_specs.emplace_back("wb700", 6.0800f);
    make_specs.emplace_back("wb750", 6.0800f);
    make_specs.emplace_back("wb800f", 6.1600f);
    make_specs.emplace_back("wb850f", 6.1600f);
    make_specs.emplace_back("wp10", 6.0800f);
  }

  {
    auto& make_specs = specs["sanyo"];
    make_specs.reserve(78);
    make_specs.emplace_back("dscs1", 5.3300f);
    make_specs.emplace_back("dscs3", 5.3300f);
    make_specs.emplace_back("dscs4", 5.7500f);
    make_specs.emplace_back("dscs5", 5.7500f);
    make_specs.emplace_back("vpca5", 5.7500f);
    make_specs.emplace_back("vpcaz1", 7.1100f);
    make_specs.emplace_back("vpcaz3ex", 7.1100f);
    make_specs.emplace_back("vpce1090", 6.1600f);
    make_specs.emplace_back("vpce1403", 6.1600f);
    make_specs.emplace_back("vpce1500tp", 6.0800f);
    make_specs.emplace_back("vpce890", 5.7500f);
    make_specs.emplace_back("vpchd1ex", 5.7500f);
    make_specs.emplace_back("vpcj1ex", 5.3300f);
    make_specs.emplace_back("vpcj2ex", 5.3300f);
    make_specs.emplace_back("vpcj4ex", 5.3300f);
    make_specs.emplace_back("vpcmz1", 7.1100f);
    make_specs.emplace_back("vpcmz2", 7.1100f);
    make_specs.emplace_back("vpcs1085", 6.0800f);
    make_specs.emplace_back("vpcs122", 6.1600f);
    make_specs.emplace_back("vpcs1275", 6.1600f);
    make_specs.emplace_back("vpcs1414", 6.0800f);
    make_specs.emplace_back("vpcs885", 5.7500f);
    make_specs.emplace_back("vpct1495", 6.0800f);
    make_specs.emplace_back("vpcx1200", 6.1600f);
    make_specs.emplace_back("vpcx1220", 6.1600f);
    make_specs.emplace_back("vpcx1420", 6.1600f);
    make_specs.emplace_back("xactic1", 5.3300f);
    make_specs.emplace_back("xactic40", 5.3300f);
    make_specs.emplace_back("xactic4", 5.3300f);
    make_specs.emplace_back("xactic5", 5.7500f);
    make_specs.emplace_back("xactic6", 5.7500f);
    make_specs.emplace_back("xactidmxca65", 5.7500f);
    make_specs.emplace_back("xactidmxca8", 5.7500f);
    make_specs.emplace_back("xactidmxcg65", 5.7500f);
    make_specs.emplace_back("xactidmxcg9", 6.0800f);
    make_specs.emplace_back("xactidmxhd700", 5.7500f);
    make_specs.emplace_back("xactidmxhd800", 5.7500f);
    make_specs.emplace_back("xactie60", 5.7500f);
    make_specs.emplace_back("xactie6", 5.7500f);
    make_specs.emplace_back("xactis50", 5.7500f);
    make_specs.emplace_back("xactis60", 5.7500f);
    make_specs.emplace_back("xactis6", 5.7500f);
    make_specs.emplace_back("xactis70", 5.7500f);
    make_specs.emplace_back("xactivpc503", 5.7500f);
    make_specs.emplace_back("xactivpc603", 5.7500f);
    make_specs.emplace_back("xactivpcca6", 5.7500f);
    make_specs.emplace_back("xactivpcca9", 5.7500f);
    make_specs.emplace_back("xactivpccg10", 6.0800f);
    make_specs.emplace_back("xactivpccg6", 5.7500f);
    make_specs.emplace_back("xactivpce1075", 6.0800f);
    make_specs.emplace_back("xactivpce10", 6.1600f);
    make_specs.emplace_back("xactivpce760", 5.7500f);
    make_specs.emplace_back("xactivpce7", 5.7500f);
    make_specs.emplace_back("xactivpce860", 5.7500f);
    make_specs.emplace_back("xactivpce870", 5.7500f);
    make_specs.emplace_back("xactivpce875", 5.7500f);
    make_specs.emplace_back("xactivpchd1a", 5.7500f);
    make_specs.emplace_back("xactivpchd2000", 5.7500f);
    make_specs.emplace_back("xactivpchd2", 5.7500f);
    make_specs.emplace_back("xactivpcs1ex", 5.3300f);
    make_specs.emplace_back("xactivpcs1070", 6.0800f);
    make_specs.emplace_back("xactivpcs3ex", 5.3300f);
    make_specs.emplace_back("xactivpcs4ex", 5.7500f);
    make_specs.emplace_back("xactivpcs500", 5.7500f);
    make_specs.emplace_back("xactivpcs600", 5.7500f);
    make_specs.emplace_back("xactivpcs650", 5.7500f);
    make_specs.emplace_back("xactivpcs670", 5.7500f);
    make_specs.emplace_back("xactivpcs750", 5.7500f);
    make_specs.emplace_back("xactivpcs760", 5.7500f);
    make_specs.emplace_back("xactivpcs770", 5.7500f);
    make_specs.emplace_back("xactivpcs7", 5.7500f);
    make_specs.emplace_back("xactivpcs870", 5.7500f);
    make_specs.emplace_back("xactivpcs880", 5.7500f);
    make_specs.emplace_back("xactivpct1060", 6.0800f);
    make_specs.emplace_back("xactivpct700", 5.7500f);
    make_specs.emplace_back("xactivpct850", 5.7500f);
    make_specs.emplace_back("xactivpcw800", 5.7500f);
    make_specs.emplace_back("xactivpcx1200", 6.1600f);
  }

  {
    auto& make_specs = specs["sigma"];
    make_specs.reserve(15);
    make_specs.emplace_back("dp1merrill", 24.0000f);
    make_specs.emplace_back("dp1s", 20.7000f);
    make_specs.emplace_back("dp1x", 20.7000f);
    make_specs.emplace_back("dp1", 20.7000f);
    make_specs.emplace_back("dp2merrill", 24.0000f);
    make_specs.emplace_back("dp2s", 20.7000f);
    make_specs.emplace_back("dp2x", 20.7000f);
    make_specs.emplace_back("dp2", 20.7000f);
    make_specs.emplace_back("dp3merrill", 24.0000f);
    make_specs.emplace_back("sd1merrill", 24.0000f);
    make_specs.emplace_back("sd10", 20.7000f);
    make_specs.emplace_back("sd14", 20.7000f);
    make_specs.emplace_back("sd15", 20.7000f);
    make_specs.emplace_back("sd1", 24.0000f);
    make_specs.emplace_back("sd9", 20.7000f);
  }

  {
    auto& make_specs = specs["sony"];
    make_specs.reserve(302);
    make_specs.emplace_back("a77ii", 23.5000f);
    make_specs.emplace_back("alpha7ii", 35.8000f);
    make_specs.emplace_back("alpha7r", 35.9000f);
    make_specs.emplace_back("alpha7rii", 35.9000f);
    make_specs.emplace_back("alpha7sii", 35.6000f);
    make_specs.emplace_back("alpha7s", 35.6000f);
    make_specs.emplace_back("alpha7", 35.8000f);
    make_specs.emplace_back("alphaa3000", 23.5000f);
    make_specs.emplace_back("alphaa5000", 23.5000f);
    make_specs.emplace_back("alphaa5100", 23.5000f);
    make_specs.emplace_back("alphaa6000", 23.5000f);
    make_specs.emplace_back("alphadslra100", 23.6000f);
    make_specs.emplace_back("alphadslra200", 23.6000f);
    make_specs.emplace_back("alphadslra230", 23.5000f);
    make_specs.emplace_back("alphadslra290", 23.5000f);
    make_specs.emplace_back("alphadslra300", 23.6000f);
    make_specs.emplace_back("alphadslra330", 23.5000f);
    make_specs.emplace_back("alphadslra350", 23.6000f);
    make_specs.emplace_back("alphadslra380", 23.6000f);
    make_specs.emplace_back("alphadslra390", 23.5000f);
    make_specs.emplace_back("alphadslra450", 23.4000f);
    make_specs.emplace_back("alphadslra500", 23.5000f);
    make_specs.emplace_back("alphadslra550", 23.4000f);
    make_specs.emplace_back("alphadslra560", 23.5000f);
    make_specs.emplace_back("alphadslra580", 23.5000f);
    make_specs.emplace_back("alphadslra700", 23.5000f);
    make_specs.emplace_back("alphadslra850", 35.9000f);
    make_specs.emplace_back("alphadslra900", 35.9000f);
    make_specs.emplace_back("alphanex3n", 23.5000f);
    make_specs.emplace_back("alphanex3", 23.4000f);
    make_specs.emplace_back("alphanex5n", 23.4000f);
    make_specs.emplace_back("alphanex5r", 23.4000f);
    make_specs.emplace_back("alphanex5t", 23.4000f);
    make_specs.emplace_back("alphanex5", 23.4000f);
    make_specs.emplace_back("alphanex7", 23.5000f);
    make_specs.emplace_back("alphanexc3", 23.4000f);
    make_specs.emplace_back("alphanexf3", 23.4000f);
    make_specs.emplace_back("alphanex6", 23.5000f);
    make_specs.emplace_back("cybershotdscrx1rii", 35.9000f);
    make_specs.emplace_back("cybershotdscrx1", 35.8000f);
    make_specs.emplace_back("cybershotdscd700", 6.4000f);
    make_specs.emplace_back("cybershotdscd770", 6.4000f);
    make_specs.emplace_back("cybershotdscf505v", 7.1100f);
    make_specs.emplace_back("cybershotdscf505", 6.4000f);
    make_specs.emplace_back("cybershotdscf55v", 7.1100f);
    make_specs.emplace_back("cybershotdscf55", 6.4000f);
    make_specs.emplace_back("cybershotdscf707", 8.8000f);
    make_specs.emplace_back("cybershotdscf717", 8.8000f);
    make_specs.emplace_back("cybershotdscf77", 7.1100f);
    make_specs.emplace_back("cybershotdscf828", 8.8000f);
    make_specs.emplace_back("cybershotdscf88", 5.9000f);
    make_specs.emplace_back("cybershotdscfx77", 7.1100f);
    make_specs.emplace_back("cybershotdscg1", 5.7500f);
    make_specs.emplace_back("cybershotdscg3", 6.1600f);
    make_specs.emplace_back("cybershotdsch100", 6.1600f);
    make_specs.emplace_back("cybershotdsch10", 5.7500f);
    make_specs.emplace_back("cybershotdsch1", 5.7500f);
    make_specs.emplace_back("cybershotdsch200", 6.1600f);
    make_specs.emplace_back("cybershotdsch20", 6.1600f);
    make_specs.emplace_back("cybershotdsch2", 5.7500f);
    make_specs.emplace_back("cybershotdsch300", 6.1600f);
    make_specs.emplace_back("cybershotdsch3", 5.7500f);
    make_specs.emplace_back("cybershotdsch400", 6.1600f);
    make_specs.emplace_back("cybershotdsch50", 6.1600f);
    make_specs.emplace_back("cybershotdsch55", 6.1600f);
    make_specs.emplace_back("cybershotdsch5", 5.7500f);
    make_specs.emplace_back("cybershotdsch70", 6.1600f);
    make_specs.emplace_back("cybershotdsch7", 5.7500f);
    make_specs.emplace_back("cybershotdsch90", 6.1600f);
    make_specs.emplace_back("cybershotdsch9", 5.7500f);
    make_specs.emplace_back("cybershotdschx100v", 6.1600f);
    make_specs.emplace_back("cybershotdschx10v", 6.1600f);
    make_specs.emplace_back("cybershotdschx1", 5.9000f);
    make_specs.emplace_back("cybershotdschx200v", 6.1600f);
    make_specs.emplace_back("cybershotdschx20v", 6.1600f);
    make_specs.emplace_back("cybershotdschx300", 6.1600f);
    make_specs.emplace_back("cybershotdschx30v", 6.1600f);
    make_specs.emplace_back("cybershotdschx400", 6.1600f);
    make_specs.emplace_back("cybershotdschx50", 6.1600f);
    make_specs.emplace_back("cybershotdschx5", 5.9000f);
    make_specs.emplace_back("cybershotdschx60", 6.1600f);
    make_specs.emplace_back("cybershotdschx7v", 6.1600f);
    make_specs.emplace_back("cybershotdschx90v", 6.1600f);
    make_specs.emplace_back("cybershotdschx9v", 6.1600f);
    make_specs.emplace_back("cybershotdscj10", 6.1600f);
    make_specs.emplace_back("cybershotdscl1", 5.3300f);
    make_specs.emplace_back("cybershotdscm1", 5.9000f);
    make_specs.emplace_back("cybershotdscm2", 5.7500f);
    make_specs.emplace_back("cybershotdscn1", 7.1100f);
    make_specs.emplace_back("cybershotdscn2", 7.5300f);
    make_specs.emplace_back("cybershotdscp100", 7.1100f);
    make_specs.emplace_back("cybershotdscp10", 7.1100f);
    make_specs.emplace_back("cybershotdscp120", 7.1100f);
    make_specs.emplace_back("cybershotdscp12", 7.1100f);
    make_specs.emplace_back("cybershotdscp150", 7.1100f);
    make_specs.emplace_back("cybershotdscp1", 7.1100f);
    make_specs.emplace_back("cybershotdscp200", 7.1100f);
    make_specs.emplace_back("cybershotdscp20", 5.3300f);
    make_specs.emplace_back("cybershotdscp2", 5.3300f);
    make_specs.emplace_back("cybershotdscp30", 5.3300f);
    make_specs.emplace_back("cybershotdscp31", 5.3300f);
    make_specs.emplace_back("cybershotdscp32", 5.3300f);
    make_specs.emplace_back("cybershotdscp3", 7.1100f);
    make_specs.emplace_back("cybershotdscp41", 5.3300f);
    make_specs.emplace_back("cybershotdscp43", 5.3300f);
    make_specs.emplace_back("cybershotdscp50", 5.3300f);
    make_specs.emplace_back("cybershotdscp51", 5.3300f);
    make_specs.emplace_back("cybershotdscp52", 5.3300f);
    make_specs.emplace_back("cybershotdscp5", 7.1100f);
    make_specs.emplace_back("cybershotdscp71", 7.1100f);
    make_specs.emplace_back("cybershotdscp72", 7.1100f);
    make_specs.emplace_back("cybershotdscp73", 5.3300f);
    make_specs.emplace_back("cybershotdscp7", 7.1100f);
    make_specs.emplace_back("cybershotdscp8", 5.3300f);
    make_specs.emplace_back("cybershotdscp92", 7.1100f);
    make_specs.emplace_back("cybershotdscp93", 7.1100f);
    make_specs.emplace_back("cybershotdscp9", 7.1100f);
    make_specs.emplace_back("cybershotdscqx100", 13.2000f);
    make_specs.emplace_back("cybershotdscqx10", 6.1600f);
    make_specs.emplace_back("cybershotdscr1", 21.5000f);
    make_specs.emplace_back("cybershotdscrx10ii", 13.2000f);
    make_specs.emplace_back("cybershotdscrx100iii", 13.2000f);
    make_specs.emplace_back("cybershotdscrx100ii", 13.2000f);
    make_specs.emplace_back("cybershotdscrx100iv", 13.2000f);
    make_specs.emplace_back("cybershotdscrx100", 13.2000f);
    make_specs.emplace_back("cybershotdscrx10", 13.2000f);
    make_specs.emplace_back("cybershotdscrx1r", 35.8000f);
    make_specs.emplace_back("cybershotdscs2000", 6.1600f);
    make_specs.emplace_back("cybershotdscs2100", 6.1600f);
    make_specs.emplace_back("cybershotdscs3000", 4.9600f);
    make_specs.emplace_back("cybershotdscs30", 5.3300f);
    make_specs.emplace_back("cybershotdscs40", 5.3300f);
    make_specs.emplace_back("cybershotdscs45", 5.7500f);
    make_specs.emplace_back("cybershotdscs5000", 6.1600f);
    make_specs.emplace_back("cybershotdscs50", 5.3300f);
    make_specs.emplace_back("cybershotdscs600", 5.7500f);
    make_specs.emplace_back("cybershotdscs60", 5.3300f);
    make_specs.emplace_back("cybershotdscs650", 5.7500f);
    make_specs.emplace_back("cybershotdscs700", 5.7500f);
    make_specs.emplace_back("cybershotdscs70", 7.1100f);
    make_specs.emplace_back("cybershotdscs730", 5.7500f);
    make_specs.emplace_back("cybershotdscs750", 5.7500f);
    make_specs.emplace_back("cybershotdscs75", 7.1100f);
    make_specs.emplace_back("cybershotdscs780", 5.7500f);
    make_specs.emplace_back("cybershotdscs800", 7.1100f);
    make_specs.emplace_back("cybershotdscs80", 5.3300f);
    make_specs.emplace_back("cybershotdscs85", 7.1100f);
    make_specs.emplace_back("cybershotdscs90", 5.3300f);
    make_specs.emplace_back("cybershotdscs930", 6.0800f);
    make_specs.emplace_back("cybershotdscs950", 6.1600f);
    make_specs.emplace_back("cybershotdscs980", 6.0800f);
    make_specs.emplace_back("cybershotdsct100", 5.7500f);
    make_specs.emplace_back("cybershotdsct10", 5.7500f);
    make_specs.emplace_back("cybershotdsct110", 6.1600f);
    make_specs.emplace_back("cybershotdsct11", 5.9000f);
    make_specs.emplace_back("cybershotdsct1", 5.9000f);
    make_specs.emplace_back("cybershotdsct200", 5.7500f);
    make_specs.emplace_back("cybershotdsct20", 5.7500f);
    make_specs.emplace_back("cybershotdsct2", 5.7500f);
    make_specs.emplace_back("cybershotdsct300", 6.1600f);
    make_specs.emplace_back("cybershotdsct30", 5.7500f);
    make_specs.emplace_back("cybershotdsct33", 5.9000f);
    make_specs.emplace_back("cybershotdsct3", 5.9000f);
    make_specs.emplace_back("cybershotdsct500", 6.1600f);
    make_specs.emplace_back("cybershotdsct50", 5.7500f);
    make_specs.emplace_back("cybershotdsct5", 5.7500f);
    make_specs.emplace_back("cybershotdsct700", 6.1600f);
    make_specs.emplace_back("cybershotdsct70", 5.7500f);
    make_specs.emplace_back("cybershotdsct77", 6.1600f);
    make_specs.emplace_back("cybershotdsct7", 5.9000f);
    make_specs.emplace_back("cybershotdsct900", 6.1600f);
    make_specs.emplace_back("cybershotdsct90", 6.1600f);
    make_specs.emplace_back("cybershotdsct99", 6.1600f);
    make_specs.emplace_back("cybershotdsct9", 5.7500f);
    make_specs.emplace_back("cybershotdsctf1", 6.1600f);
    make_specs.emplace_back("cybershotdsctx100v", 6.1600f);
    make_specs.emplace_back("cybershotdsctx10", 6.1600f);
    make_specs.emplace_back("cybershotdsctx1", 5.9000f);
    make_specs.emplace_back("cybershotdsctx200v", 6.1600f);
    make_specs.emplace_back("cybershotdsctx20", 6.1600f);
    make_specs.emplace_back("cybershotdsctx30", 6.1600f);
    make_specs.emplace_back("cybershotdsctx55", 6.1600f);
    make_specs.emplace_back("cybershotdsctx5", 5.9000f);
    make_specs.emplace_back("cybershotdsctx66", 6.1600f);
    make_specs.emplace_back("cybershotdsctx7", 5.9000f);
    make_specs.emplace_back("cybershotdsctx9", 6.1600f);
    make_specs.emplace_back("cybershotdscu10", 5.3300f);
    make_specs.emplace_back("cybershotdscu20", 5.3300f);
    make_specs.emplace_back("cybershotdscu30", 5.3300f);
    make_specs.emplace_back("cybershotdscu40", 5.3300f);
    make_specs.emplace_back("cybershotdscu50", 5.3300f);
    make_specs.emplace_back("cybershotdscu60", 5.3300f);
    make_specs.emplace_back("cybershotdscv1", 7.1100f);
    make_specs.emplace_back("cybershotdscv3", 7.1100f);
    make_specs.emplace_back("cybershotdscw100", 7.1100f);
    make_specs.emplace_back("cybershotdscw110", 5.7500f);
    make_specs.emplace_back("cybershotdscw115", 5.7500f);
    make_specs.emplace_back("cybershotdscw120", 5.7500f);
    make_specs.emplace_back("cybershotdscw125", 5.7500f);
    make_specs.emplace_back("cybershotdscw12", 7.1100f);
    make_specs.emplace_back("cybershotdscw130", 5.7500f);
    make_specs.emplace_back("cybershotdscw150", 5.7500f);
    make_specs.emplace_back("cybershotdscw170", 6.1600f);
    make_specs.emplace_back("cybershotdscw17", 7.1100f);
    make_specs.emplace_back("cybershotdscw180", 6.0800f);
    make_specs.emplace_back("cybershotdscw190", 6.0800f);
    make_specs.emplace_back("cybershotdscw1", 7.1100f);
    make_specs.emplace_back("cybershotdscw200", 7.5300f);
    make_specs.emplace_back("cybershotdscw210", 6.1600f);
    make_specs.emplace_back("cybershotdscw215", 6.1600f);
    make_specs.emplace_back("cybershotdscw220", 6.1600f);
    make_specs.emplace_back("cybershotdscw230", 6.1600f);
    make_specs.emplace_back("cybershotdscw270", 7.5300f);
    make_specs.emplace_back("cybershotdscw275", 6.1600f);
    make_specs.emplace_back("cybershotdscw290", 6.1600f);
    make_specs.emplace_back("cybershotdscw300", 7.5300f);
    make_specs.emplace_back("cybershotdscw30", 5.7500f);
    make_specs.emplace_back("cybershotdscw310", 6.1600f);
    make_specs.emplace_back("cybershotdscw320", 6.1600f);
    make_specs.emplace_back("cybershotdscw330", 6.1600f);
    make_specs.emplace_back("cybershotdscw350", 6.1600f);
    make_specs.emplace_back("cybershotdscw35", 5.7500f);
    make_specs.emplace_back("cybershotdscw360", 6.1600f);
    make_specs.emplace_back("cybershotdscw370", 6.1600f);
    make_specs.emplace_back("cybershotdscw380", 6.1600f);
    make_specs.emplace_back("cybershotdscw40", 5.7500f);
    make_specs.emplace_back("cybershotdscw50", 5.7500f);
    make_specs.emplace_back("cybershotdscw510", 6.1600f);
    make_specs.emplace_back("cybershotdscw520", 6.1600f);
    make_specs.emplace_back("cybershotdscw530", 6.1600f);
    make_specs.emplace_back("cybershotdscw550", 6.1600f);
    make_specs.emplace_back("cybershotdscw55", 5.7500f);
    make_specs.emplace_back("cybershotdscw560", 6.1600f);
    make_specs.emplace_back("cybershotdscw570", 6.1600f);
    make_specs.emplace_back("cybershotdscw580", 6.1600f);
    make_specs.emplace_back("cybershotdscw5", 7.1100f);
    make_specs.emplace_back("cybershotdscw610", 6.1600f);
    make_specs.emplace_back("cybershotdscw620", 6.1600f);
    make_specs.emplace_back("cybershotdscw630", 6.1600f);
    make_specs.emplace_back("cybershotdscw650", 6.1600f);
    make_specs.emplace_back("cybershotdscw670", 6.1600f);
    make_specs.emplace_back("cybershotdscw690", 6.1600f);
    make_specs.emplace_back("cybershotdscw70", 5.7500f);
    make_specs.emplace_back("cybershotdscw710", 6.1600f);
    make_specs.emplace_back("cybershotdscw730", 6.1600f);
    make_specs.emplace_back("cybershotdscw7", 7.1100f);
    make_specs.emplace_back("cybershotdscw800", 6.1600f);
    make_specs.emplace_back("cybershotdscw80", 5.7500f);
    make_specs.emplace_back("cybershotdscw810", 6.1600f);
    make_specs.emplace_back("cybershotdscw830", 6.1600f);
    make_specs.emplace_back("cybershotdscw85", 5.7500f);
    make_specs.emplace_back("cybershotdscw90", 5.7500f);
    make_specs.emplace_back("cybershotdscwx100", 6.1600f);
    make_specs.emplace_back("cybershotdscwx10", 6.1600f);
    make_specs.emplace_back("cybershotdscwx150", 6.1600f);
    make_specs.emplace_back("cybershotdscwx1", 5.9000f);
    make_specs.emplace_back("cybershotdscwx200", 6.1600f);
    make_specs.emplace_back("cybershotdscwx220", 6.1600f);
    make_specs.emplace_back("cybershotdscwx300", 6.1600f);
    make_specs.emplace_back("cybershotdscwx30", 6.1600f);
    make_specs.emplace_back("cybershotdscwx350", 6.1600f);
    make_specs.emplace_back("cybershotdscwx500", 6.1600f);
    make_specs.emplace_back("cybershotdscwx50", 6.1600f);
    make_specs.emplace_back("cybershotdscwx5", 6.1600f);
    make_specs.emplace_back("cybershotdscwx60", 6.1600f);
    make_specs.emplace_back("cybershotdscwx70", 6.1600f);
    make_specs.emplace_back("cybershotdscwx7", 6.1600f);
    make_specs.emplace_back("cybershotdscwx80", 6.1600f);
    make_specs.emplace_back("cybershotdscwx9", 6.1600f);
    make_specs.emplace_back("mavicacd1000", 5.3300f);
    make_specs.emplace_back("mavicacd200", 5.3300f);
    make_specs.emplace_back("mavicacd250", 5.3300f);
    make_specs.emplace_back("mavicacd300", 7.1100f);
    make_specs.emplace_back("mavicacd350", 5.3300f);
    make_specs.emplace_back("mavicacd400", 7.1100f);
    make_specs.emplace_back("mavicacd500", 7.1100f);
    make_specs.emplace_back("mavicafd100", 5.3300f);
    make_specs.emplace_back("mavicafd200", 5.3300f);
    make_specs.emplace_back("mavicafd71", 6.4000f);
    make_specs.emplace_back("mavicafd73", 6.4000f);
    make_specs.emplace_back("mavicafd75", 7.1100f);
    make_specs.emplace_back("mavicafd81", 4.8000f);
    make_specs.emplace_back("mavicafd83", 4.8000f);
    make_specs.emplace_back("mavicafd85", 5.3300f);
    make_specs.emplace_back("mavicafd87", 5.3300f);
    make_specs.emplace_back("mavicafd88", 4.8000f);
    make_specs.emplace_back("mavicafd90", 5.3300f);
    make_specs.emplace_back("mavicafd91", 4.8000f);
    make_specs.emplace_back("mavicafd92", 5.3300f);
    make_specs.emplace_back("mavicafd95", 5.3300f);
    make_specs.emplace_back("mavicafd97", 5.3300f);
    make_specs.emplace_back("qx1", 23.5000f);
    make_specs.emplace_back("qx30", 6.1600f);
    make_specs.emplace_back("slta33", 23.5000f);
    make_specs.emplace_back("slta35", 23.5000f);
    make_specs.emplace_back("slta37", 23.5000f);
    make_specs.emplace_back("slta55", 23.5000f);
    make_specs.emplace_back("slta57", 23.5000f);
    make_specs.emplace_back("slta58", 23.2000f);
    make_specs.emplace_back("slta65", 23.5000f);
    make_specs.emplace_back("slta77", 23.5000f);
    make_specs.emplace_back("slta99", 35.8000f);
  }

  {
    auto& make_specs = specs["toshiba"];
    make_specs.reserve(19);
    make_specs.emplace_back("pdr2300", 5.3300f);
    make_specs.emplace_back("pdr3300", 7.1100f);
    make_specs.emplace_back("pdr3310", 7.1100f);
    make_specs.emplace_back("pdr3320", 7.1100f);
    make_specs.emplace_back("pdr4300", 7.1100f);
    make_specs.emplace_back("pdr5300", 7.1100f);
    make_specs.emplace_back("pdrm25", 5.3300f);
    make_specs.emplace_back("pdrm500", 5.3300f);
    make_specs.emplace_back("pdrm5", 6.4000f);
    make_specs.emplace_back("pdrm60", 6.4000f);
    make_specs.emplace_back("pdrm61", 6.4000f);
    make_specs.emplace_back("pdrm65", 6.4000f);
    make_specs.emplace_back("pdrm700", 5.3300f);
    make_specs.emplace_back("pdrm70", 7.1100f);
    make_specs.emplace_back("pdrm71", 7.1100f);
    make_specs.emplace_back("pdrm81", 7.1100f);
    make_specs.emplace_back("pdrt10", 5.3300f);
    make_specs.emplace_back("pdrt20", 5.3300f);
    make_specs.emplace_back("pdrt30", 5.3300f);
  }

  {
    auto& make_specs = specs["vivitar"];
    make_specs.reserve(30);
    make_specs.emplace_back("v8025", 7.1100f);
    make_specs.emplace_back("vivicam5105s", 5.7500f);
    make_specs.emplace_back("vivicam5150s", 5.7500f);
    make_specs.emplace_back("vivicam5160s", 5.7500f);
    make_specs.emplace_back("vivicam5195", 5.7500f);
    make_specs.emplace_back("vivicam5350s", 5.7500f);
    make_specs.emplace_back("vivicam5355", 5.7500f);
    make_specs.emplace_back("vivicam5385", 5.7500f);
    make_specs.emplace_back("vivicam5386", 5.7500f);
    make_specs.emplace_back("vivicam5388", 5.7500f);
    make_specs.emplace_back("vivicam6150s", 5.7500f);
    make_specs.emplace_back("vivicam6200w", 5.7500f);
    make_specs.emplace_back("vivicam6300", 5.7500f);
    make_specs.emplace_back("vivicam6320", 5.7500f);
    make_specs.emplace_back("vivicam6326", 5.7500f);
    make_specs.emplace_back("vivicam6330", 5.7500f);
    make_specs.emplace_back("vivicam6380u", 5.7500f);
    make_specs.emplace_back("vivicam6385u", 5.7500f);
    make_specs.emplace_back("vivicam6388s", 5.7500f);
    make_specs.emplace_back("vivicam7100s", 5.7500f);
    make_specs.emplace_back("vivicam7310", 5.7500f);
    make_specs.emplace_back("vivicam7388s", 5.7500f);
    make_specs.emplace_back("vivicam7500i", 5.7500f);
    make_specs.emplace_back("vivicam8300s", 7.1100f);
    make_specs.emplace_back("vivicam8400", 7.1100f);
    make_specs.emplace_back("vivicam8600s", 7.1100f);
    make_specs.emplace_back("vivicam8600", 7.1100f);
    make_specs.emplace_back("vivicam8625", 7.1100f);
    make_specs.emplace_back("vivicamx30", 7.1100f);
    make_specs.emplace_back("vivicamx60", 7.1100f);
  }

  {
    auto& make_specs = specs["yakumo"];
    make_specs.reserve(25);
    make_specs.emplace_back("cammastersd432", 5.3300f);
    make_specs.emplace_back("cammastersd482", 5.7500f);
    make_specs.emplace_back("megaimage34", 5.3300f);
    make_specs.emplace_back("megaimage35", 7.1100f);
    make_specs.emplace_back("megaimage37", 5.7500f);
    make_specs.emplace_back("megaimage410", 5.7500f);
    make_specs.emplace_back("megaimage45", 7.1100f);
    make_specs.emplace_back("megaimage47sl", 5.7500f);
    make_specs.emplace_back("megaimage47sx", 7.1100f);
    make_specs.emplace_back("megaimage47", 5.7500f);
    make_specs.emplace_back("megaimage55cx", 7.1100f);
    make_specs.emplace_back("megaimage57x", 7.1100f);
    make_specs.emplace_back("megaimage57", 7.1100f);
    make_specs.emplace_back("megaimage610x", 7.1100f);
    make_specs.emplace_back("megaimage67x", 7.1100f);
    make_specs.emplace_back("megaimage811x", 7.1100f);
    make_specs.emplace_back("megaimage84d", 5.7500f);
    make_specs.emplace_back("megaimage85d", 5.7500f);
    make_specs.emplace_back("megaimageii", 7.1100f);
    make_specs.emplace_back("megaimageiv", 7.1100f);
    make_specs.emplace_back("megaimagevii", 6.4000f);
    make_specs.emplace_back("megaimagevi", 7.1100f);
    make_specs.emplace_back("megaimagexl", 4.2300f);
    make_specs.emplace_back("megaimagexs", 6.4000f);
    make_specs.emplace_back("megaimagex", 7.1100f);
  }

  return specs;
}

}  // namespace colmap
