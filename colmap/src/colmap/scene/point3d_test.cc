// Copyright (c), ETH Zurich and UNC Chapel Hill.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//
//     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
//       its contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

#include "colmap/scene/point3d.h"

#include <gtest/gtest.h>

namespace colmap {
namespace {

TEST(Point3D, Default) {
  Point3D point3D;
  EXPECT_EQ(point3D.xyz, Eigen::Vector3d::Zero());
  EXPECT_EQ(point3D.color, Eigen::Vector3ub::Zero());
  EXPECT_EQ(point3D.error, -1.0);
  EXPECT_FALSE(point3D.HasError());
  EXPECT_EQ(point3D.track.Length(), 0);
}

TEST(Point3D, Equals) {
  Point3D point3D;
  Point3D other = point3D;
  EXPECT_EQ(point3D, other);
  point3D.xyz(0) += 1;
  EXPECT_NE(point3D, other);
  other.xyz(0) += 1;
  EXPECT_EQ(point3D, other);
}

TEST(Point3D, Print) {
  Point3D point3D;
  point3D.xyz = Eigen::Vector3d(1, 2, 3);
  std::ostringstream stream;
  stream << point3D;
  EXPECT_EQ(stream.str(), "Point3D(xyz=[1, 2, 3], track_len=0)");
}

TEST(Point3D, Error) {
  Point3D point3D;
  EXPECT_EQ(point3D.error, -1.0);
  EXPECT_FALSE(point3D.HasError());
  point3D.error = 1.0;
  EXPECT_EQ(point3D.error, 1.0);
  EXPECT_TRUE(point3D.HasError());
}

}  // namespace
}  // namespace colmap
