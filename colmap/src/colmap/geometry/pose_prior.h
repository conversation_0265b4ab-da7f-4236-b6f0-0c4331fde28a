// Copyright (c), ETH Zurich and UNC Chapel Hill.
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//
//     * Neither the name of ETH Zurich and UNC Chapel Hill nor the names of
//       its contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

#pragma once

#include "colmap/util/eigen_alignment.h"
#include "colmap/util/enum_utils.h"
#include "colmap/util/types.h"

#include <ostream>

#include <Eigen/Core>

namespace colmap {

struct PosePrior {
 public:
  MAKE_ENUM_CLASS(CoordinateSystem,
                  -1,
                  UNDEFINED,  // = -1
                  WGS84,      // = 0
                  CARTESIAN   // = 1
  );

  Eigen::Vector3d position =
      Eigen::Vector3d::Constant(std::numeric_limits<double>::quiet_NaN());
  Eigen::Matrix3d position_covariance =
      Eigen::Matrix3d::Constant(std::numeric_limits<double>::quiet_NaN());
  CoordinateSystem coordinate_system = CoordinateSystem::UNDEFINED;

  PosePrior() = default;
  explicit PosePrior(const Eigen::Vector3d& position) : position(position) {}
  PosePrior(const Eigen::Vector3d& position, const CoordinateSystem system)
      : position(position), coordinate_system(system) {}
  PosePrior(const Eigen::Vector3d& position, const Eigen::Matrix3d& covariance)
      : position(position), position_covariance(covariance) {}
  PosePrior(const Eigen::Vector3d& position,
            const Eigen::Matrix3d& covariance,
            const CoordinateSystem system)
      : position(position),
        position_covariance(covariance),
        coordinate_system(system) {}

  inline bool IsValid() const { return position.allFinite(); }
  inline bool IsCovarianceValid() const {
    return position_covariance.allFinite();
  }

  inline bool operator==(const PosePrior& other) const;
  inline bool operator!=(const PosePrior& other) const;
};

std::ostream& operator<<(std::ostream& stream, const PosePrior& prior);

bool PosePrior::operator==(const PosePrior& other) const {
  return coordinate_system == other.coordinate_system &&
         position == other.position &&
         position_covariance == other.position_covariance;
}

bool PosePrior::operator!=(const PosePrior& other) const {
  return !(*this == other);
}

}  // namespace colmap
