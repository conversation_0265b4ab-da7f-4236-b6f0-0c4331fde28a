import os
from PIL import Image
from pathlib import Path

def check_images_in_folder(folder_path):
    """
    检查文件夹中的所有图片是否损坏
    :param folder_path: 文件夹路径
    :return: (损坏图片列表, 总图片数)
    """
    corrupted_images = []
    total_images = 0
    supported_formats = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', 'JPG')

    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(supported_formats):
                total_images += 1
                file_path = os.path.join(root, file)
                try:
                    # 尝试打开图片
                    with Image.open(file_path) as img:
                        img.verify()  # 验证基本完整性
                    
                except Exception as e:
                    corrupted_images.append((file_path, str(e)))
    
    return corrupted_images, total_images

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='检查图片文件是否损坏')
    parser.add_argument('folder', help='要检查的文件夹路径')
    args = parser.parse_args()

    corrupted, total = check_images_in_folder(args.folder)
    
    print(f"\n检查完成! 共扫描 {total} 张图片")
    if corrupted:
        print(f"发现 {len(corrupted)} 张损坏图片:")
        for i, (path, error) in enumerate(corrupted, 1):
            print(f"{i}. {path} - 错误: {error}")
    else:
        print("所有图片均完好无损 ✓")

    # 退出码: 有损坏图片返回1，否则返回0
    exit(1 if corrupted else 0)